# PTE Proficiency Backend API

A comprehensive Node.js backend API for the PTE Proficiency Learning Platform, built with Express.js and MongoDB.

## Features

### 🔐 Authentication & Authorization
- **JWT-based authentication** with access and refresh tokens
- **Role-based access control** (Student, Instructor, Admin)
- **Password hashing** with bcrypt
- **Email verification** and password reset
- **Rate limiting** for security
- **Session management** with secure cookies

### 📚 Course Management
- **CRUD operations** for courses and lessons
- **Course enrollment** and progress tracking
- **Review and rating system**
- **Category and level filtering**
- **Search functionality**
- **Instructor management**

### 👥 User Management
- **User profiles** with preferences
- **Study progress tracking**
- **Subscription management**
- **Learning analytics**
- **Study streak tracking**

### 🛡️ Security Features
- **Helmet.js** for security headers
- **CORS** configuration
- **Input validation** with express-validator
- **Rate limiting** to prevent abuse
- **Error handling** and logging

## Tech Stack

- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MongoDB** - Database
- **Mongoose** - ODM for MongoDB
- **JWT** - Authentication tokens
- **bcryptjs** - Password hashing
- **express-validator** - Input validation
- **helmet** - Security middleware
- **cors** - Cross-origin resource sharing
- **morgan** - HTTP request logger

## Getting Started

### Prerequisites
- Node.js 18+
- MongoDB 5.0+
- npm or yarn

### Installation

1. **Navigate to server directory**
   ```bash
   cd server
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start MongoDB**
   ```bash
   # Using MongoDB service
   sudo systemctl start mongod
   
   # Or using Docker
   docker run -d -p 27017:27017 --name mongodb mongo:latest
   ```

5. **Seed the database (optional)**
   ```bash
   npm run seed
   ```

6. **Start the server**
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm start
   ```

## API Endpoints

### Authentication
```
POST   /api/v1/auth/register      - Register new user
POST   /api/v1/auth/login         - Login user
POST   /api/v1/auth/logout        - Logout user
GET    /api/v1/auth/me            - Get current user
POST   /api/v1/auth/refresh       - Refresh access token
POST   /api/v1/auth/forgot-password - Request password reset
PUT    /api/v1/auth/reset-password/:token - Reset password
PUT    /api/v1/auth/change-password - Change password
```

### Courses
```
GET    /api/v1/courses            - Get all courses (with filters)
GET    /api/v1/courses/:id        - Get single course
POST   /api/v1/courses            - Create course (Instructor/Admin)
PUT    /api/v1/courses/:id        - Update course (Instructor/Admin)
DELETE /api/v1/courses/:id        - Delete course (Instructor/Admin)
POST   /api/v1/courses/:id/enroll - Enroll in course
GET    /api/v1/courses/my/enrolled - Get enrolled courses
POST   /api/v1/courses/:id/reviews - Add course review
```

### Health Check
```
GET    /health                    - Server health status
```

## Environment Variables

```env
# Server Configuration
NODE_ENV=development
PORT=5000
API_VERSION=v1

# Database
MONGODB_URI=mongodb://localhost:27017/pte-proficiency

# JWT Configuration
JWT_SECRET=your-jwt-secret
JWT_EXPIRE=7d
JWT_REFRESH_SECRET=your-refresh-secret
JWT_REFRESH_EXPIRE=30d

# Frontend URL
FRONTEND_URL=http://localhost:5174

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## Database Models

### User Model
- Personal information (name, email, avatar)
- Authentication data (password, tokens)
- Role and permissions
- Learning progress and statistics
- Subscription information
- Preferences and settings

### Course Model
- Course details (title, description, category)
- Instructor information
- Pricing and enrollment data
- Lessons and content structure
- Reviews and ratings
- Analytics and statistics

### Lesson Model
- Lesson content (video, audio, text, quiz)
- Progress tracking
- Prerequisites and dependencies
- Analytics and completion data

## API Response Format

### Success Response
```json
{
  "success": true,
  "message": "Operation successful",
  "data": {
    // Response data
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    // Validation errors (if any)
  ]
}
```

## Authentication

The API uses JWT tokens for authentication:

1. **Login** to receive access and refresh tokens
2. **Include access token** in Authorization header: `Bearer <token>`
3. **Refresh token** when access token expires
4. **Logout** to invalidate tokens

## Rate Limiting

- **100 requests per 15 minutes** per IP address
- **Sensitive operations** have additional rate limiting
- **429 status code** returned when limit exceeded

## Error Handling

The API includes comprehensive error handling for:
- Validation errors
- Authentication errors
- Database errors
- Server errors
- CORS violations

## Development

### Available Scripts
```bash
npm start          # Start production server
npm run dev        # Start development server with nodemon
npm run seed       # Seed database with sample data
npm test           # Run tests (when implemented)
```

### Code Structure
```
server/
├── config/         # Database and configuration
├── controllers/    # Route handlers
├── middleware/     # Custom middleware
├── models/         # Database models
├── routes/         # API routes
├── scripts/        # Utility scripts
├── server.js       # Main server file
└── package.json    # Dependencies and scripts
```

## Security Considerations

- **Environment variables** for sensitive data
- **Password hashing** with bcrypt
- **JWT token expiration** and refresh mechanism
- **Rate limiting** to prevent abuse
- **Input validation** and sanitization
- **CORS** configuration for frontend access
- **Helmet.js** for security headers

## Future Enhancements

- [ ] File upload with Cloudinary
- [ ] Email service integration
- [ ] Payment processing with Stripe
- [ ] OAuth integration (Google, Microsoft, Apple)
- [ ] Real-time features with Socket.io
- [ ] Caching with Redis
- [ ] API documentation with Swagger
- [ ] Unit and integration tests
- [ ] Docker containerization
- [ ] CI/CD pipeline

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests (when test suite is available)
5. Submit a pull request

## License

This project is licensed under the MIT License.
