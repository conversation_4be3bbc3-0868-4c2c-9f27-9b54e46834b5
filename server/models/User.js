import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

const userSchema = new mongoose.Schema({
  firstName: {
    type: String,
    required: [true, 'First name is required'],
    trim: true,
    maxlength: [50, 'First name cannot exceed 50 characters']
  },
  lastName: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true,
    maxlength: [50, 'Last name cannot exceed 50 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      'Please enter a valid email'
    ]
  },
  password: {
    type: String,
    required: function() {
      return !this.ssoProvider; // Password required only if not SSO user
    },
    minlength: [6, 'Password must be at least 6 characters'],
    select: false // Don't include password in queries by default
  },
  role: {
    type: String,
    enum: ['student', 'instructor', 'admin'],
    default: 'student'
  },
  avatar: {
    public_id: String,
    url: String
  },
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: String,
  emailVerificationExpire: Date,
  resetPasswordToken: String,
  resetPasswordExpire: Date,

  // SSO Authentication
  ssoProvider: {
    type: String,
    enum: ['google', 'microsoft', 'apple'],
    default: null
  },
  ssoId: String,

  // Profile Information
  dateOfBirth: Date,
  phoneNumber: String,
  country: String,
  targetScore: {
    type: Number,
    min: 10,
    max: 90
  },
  currentLevel: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced'],
    default: 'beginner'
  },

  // Learning Progress
  enrolledCourses: [{
    course: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Course'
    },
    enrolledAt: {
      type: Date,
      default: Date.now
    },
    progress: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    completedLessons: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Lesson'
    }],
    lastAccessedAt: Date
  }],

  // Test Results
  testResults: [{
    testType: {
      type: String,
      enum: ['mock', 'practice', 'assessment']
    },
    section: {
      type: String,
      enum: ['speaking', 'writing', 'reading', 'listening', 'overall']
    },
    score: Number,
    maxScore: Number,
    completedAt: {
      type: Date,
      default: Date.now
    },
    details: mongoose.Schema.Types.Mixed
  }],

  // Subscription & Payment
  subscription: {
    plan: {
      type: String,
      enum: ['free', 'basic', 'premium', 'vip'],
      default: 'free'
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'cancelled', 'expired'],
      default: 'active'
    },
    startDate: Date,
    endDate: Date,
    stripeCustomerId: String,
    stripeSubscriptionId: String
  },

  // Preferences
  preferences: {
    notifications: {
      email: {
        type: Boolean,
        default: true
      },
      push: {
        type: Boolean,
        default: true
      },
      marketing: {
        type: Boolean,
        default: false
      }
    },
    language: {
      type: String,
      default: 'en'
    },
    timezone: String,
    studyReminders: {
      enabled: {
        type: Boolean,
        default: false
      },
      time: String,
      days: [String]
    }
  },

  // Activity Tracking
  lastLoginAt: Date,
  loginCount: {
    type: Number,
    default: 0
  },
  studyStreak: {
    current: {
      type: Number,
      default: 0
    },
    longest: {
      type: Number,
      default: 0
    },
    lastStudyDate: Date
  },

  // Account Status
  isActive: {
    type: Boolean,
    default: true
  },
  isBlocked: {
    type: Boolean,
    default: false
  },
  blockedReason: String,
  blockedAt: Date

}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
userSchema.index({ email: 1 });
userSchema.index({ role: 1 });
userSchema.index({ 'subscription.plan': 1 });
userSchema.index({ isActive: 1, isBlocked: 1 });

// Virtual for full name
userSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Virtual for total study hours (calculated from enrolled courses)
userSchema.virtual('totalStudyHours').get(function() {
  // This would be calculated based on course progress and time spent
  if (!this.enrolledCourses || this.enrolledCourses.length === 0) {
    return 0;
  }
  return this.enrolledCourses.reduce((total, enrollment) => {
    return total + (enrollment.progress * 0.5); // Rough calculation
  }, 0);
});

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) {
    return next();
  }

  const salt = await bcrypt.genSalt(12);
  this.password = await bcrypt.hash(this.password, salt);
  next();
});

// Method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Method to generate JWT token
userSchema.methods.generateAuthToken = function() {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    console.error('JWT_SECRET is not defined in environment variables');
    throw new Error('JWT_SECRET is not configured');
  }

  return jwt.sign(
    {
      id: this._id,
      email: this.email,
      role: this.role
    },
    secret,
    { expiresIn: process.env.JWT_EXPIRE || '7d' }
  );
};

// Method to generate refresh token
userSchema.methods.generateRefreshToken = function() {
  return jwt.sign(
    { id: this._id },
    process.env.JWT_REFRESH_SECRET,
    { expiresIn: process.env.JWT_REFRESH_EXPIRE }
  );
};

// Method to update study streak
userSchema.methods.updateStudyStreak = function() {
  const today = new Date();
  const lastStudy = this.studyStreak.lastStudyDate;

  if (!lastStudy) {
    this.studyStreak.current = 1;
    this.studyStreak.lastStudyDate = today;
  } else {
    const daysDiff = Math.floor((today - lastStudy) / (1000 * 60 * 60 * 24));

    if (daysDiff === 1) {
      // Consecutive day
      this.studyStreak.current += 1;
      this.studyStreak.lastStudyDate = today;
    } else if (daysDiff > 1) {
      // Streak broken
      this.studyStreak.current = 1;
      this.studyStreak.lastStudyDate = today;
    }
    // If daysDiff === 0, same day, no change needed
  }

  // Update longest streak if current is higher
  if (this.studyStreak.current > this.studyStreak.longest) {
    this.studyStreak.longest = this.studyStreak.current;
  }
};

export default mongoose.model('User', userSchema);
