import mongoose from 'mongoose';

const courseSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Course title is required'],
    trim: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Course description is required'],
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  shortDescription: {
    type: String,
    maxlength: [200, 'Short description cannot exceed 200 characters']
  },

  // Course Classification
  category: {
    type: String,
    required: [true, 'Course category is required'],
    enum: ['speaking', 'writing', 'reading', 'listening', 'mock-tests', 'comprehensive']
  },
  level: {
    type: String,
    required: [true, 'Course level is required'],
    enum: ['beginner', 'intermediate', 'advanced', 'all']
  },
  tags: [String],

  // Course Content
  lessons: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Lesson'
  }],
  totalLessons: {
    type: Number,
    default: 0
  },
  estimatedDuration: {
    hours: Number,
    minutes: Number
  },

  // Instructor Information
  instructor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Instructor is required']
  },
  coInstructors: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],

  // Pricing
  pricing: {
    type: {
      type: String,
      enum: ['free', 'paid', 'subscription'],
      default: 'paid'
    },
    price: {
      type: Number,
      default: 0,
      min: 0
    },
    originalPrice: Number,
    currency: {
      type: String,
      default: 'USD'
    },
    discountPercentage: {
      type: Number,
      min: 0,
      max: 100
    },
    subscriptionPlans: [{
      plan: {
        type: String,
        enum: ['basic', 'premium', 'vip']
      },
      included: Boolean
    }]
  },

  // Course Media
  thumbnail: {
    public_id: String,
    url: String
  },
  previewVideo: {
    public_id: String,
    url: String,
    duration: Number
  },

  // Course Features
  features: [String],
  learningOutcomes: [String],
  prerequisites: [String],

  // Course Settings
  isPublished: {
    type: Boolean,
    default: false
  },
  isVip: {
    type: Boolean,
    default: false
  },
  allowPreview: {
    type: Boolean,
    default: true
  },
  maxEnrollments: Number,

  // Enrollment & Progress
  enrollments: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    enrolledAt: {
      type: Date,
      default: Date.now
    },
    progress: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    completedAt: Date,
    certificateIssued: {
      type: Boolean,
      default: false
    }
  }],

  // Statistics
  stats: {
    totalEnrollments: {
      type: Number,
      default: 0
    },
    activeEnrollments: {
      type: Number,
      default: 0
    },
    completionRate: {
      type: Number,
      default: 0
    },
    averageRating: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    totalRatings: {
      type: Number,
      default: 0
    },
    totalRevenue: {
      type: Number,
      default: 0
    }
  },

  // Reviews and Ratings
  reviews: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    rating: {
      type: Number,
      required: true,
      min: 1,
      max: 5
    },
    comment: {
      type: String,
      maxlength: [500, 'Review comment cannot exceed 500 characters']
    },
    createdAt: {
      type: Date,
      default: Date.now
    },
    isVerified: {
      type: Boolean,
      default: false
    }
  }],

  // Course Schedule (for live courses)
  schedule: {
    type: {
      type: String,
      enum: ['self-paced', 'scheduled', 'live'],
      default: 'self-paced'
    },
    startDate: Date,
    endDate: Date,
    timezone: String,
    sessions: [{
      title: String,
      date: Date,
      duration: Number, // in minutes
      meetingLink: String,
      isRecorded: Boolean
    }]
  },

  // SEO and Marketing
  seo: {
    metaTitle: String,
    metaDescription: String,
    keywords: [String],
    slug: {
      type: String,
      unique: true,
      sparse: true
    }
  },

  // Course Status
  status: {
    type: String,
    enum: ['draft', 'review', 'published', 'archived'],
    default: 'draft'
  },
  publishedAt: Date,
  archivedAt: Date,

  // Analytics
  analytics: {
    views: {
      type: Number,
      default: 0
    },
    uniqueViews: {
      type: Number,
      default: 0
    },
    conversionRate: {
      type: Number,
      default: 0
    },
    dropoffPoints: [mongoose.Schema.Types.Mixed]
  }

}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
courseSchema.index({ category: 1, level: 1 });
courseSchema.index({ isPublished: 1, status: 1 });
courseSchema.index({ 'pricing.type': 1, 'pricing.price': 1 });
courseSchema.index({ 'stats.averageRating': -1 });
courseSchema.index({ createdAt: -1 });
courseSchema.index({ 'seo.slug': 1 });

// Virtual for discounted price
courseSchema.virtual('discountedPrice').get(function() {
  if (this.pricing.discountPercentage && this.pricing.discountPercentage > 0) {
    return this.pricing.price * (1 - this.pricing.discountPercentage / 100);
  }
  return this.pricing.price;
});

// Virtual for enrollment count
courseSchema.virtual('enrollmentCount').get(function() {
  return this.enrollments ? this.enrollments.length : 0;
});

// Virtual for completion rate
courseSchema.virtual('completionRate').get(function() {
  if (!this.enrollments || this.enrollments.length === 0) return 0;
  const completed = this.enrollments.filter(e => e.progress === 100).length;
  return (completed / this.enrollments.length) * 100;
});

// Pre-save middleware to generate slug
courseSchema.pre('save', function(next) {
  if (this.isModified('title') && !this.seo.slug) {
    this.seo.slug = this.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }
  next();
});

// Method to calculate average rating
courseSchema.methods.calculateAverageRating = function() {
  if (!this.reviews || this.reviews.length === 0) {
    this.stats.averageRating = 0;
    this.stats.totalRatings = 0;
    return;
  }

  const totalRating = this.reviews.reduce((sum, review) => sum + review.rating, 0);
  this.stats.averageRating = totalRating / this.reviews.length;
  this.stats.totalRatings = this.reviews.length;
};

// Method to update enrollment stats
courseSchema.methods.updateEnrollmentStats = function() {
  this.stats.totalEnrollments = this.enrollments ? this.enrollments.length : 0;
  this.stats.activeEnrollments = this.enrollments ? this.enrollments.filter(e => e.progress < 100).length : 0;
  this.stats.completionRate = this.completionRate;
};

// Static method to get popular courses
courseSchema.statics.getPopularCourses = function(limit = 10) {
  return this.find({ isPublished: true })
    .sort({ 'stats.totalEnrollments': -1, 'stats.averageRating': -1 })
    .limit(limit)
    .populate('instructor', 'firstName lastName avatar')
    .select('-enrollments -reviews');
};

// Static method to get courses by category
courseSchema.statics.getCoursesByCategory = function(category, options = {}) {
  const query = { category, isPublished: true };

  if (options.level) query.level = options.level;
  if (options.priceRange) {
    query['pricing.price'] = {
      $gte: options.priceRange.min || 0,
      $lte: options.priceRange.max || Number.MAX_VALUE
    };
  }

  return this.find(query)
    .sort(options.sort || { createdAt: -1 })
    .limit(options.limit || 20)
    .populate('instructor', 'firstName lastName avatar')
    .select('-enrollments -reviews');
};

export default mongoose.model('Course', courseSchema);
