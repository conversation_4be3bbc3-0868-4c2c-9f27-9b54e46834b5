import mongoose from 'mongoose';

const lessonSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Lesson title is required'],
    trim: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  
  // Lesson Organization
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: [true, 'Course reference is required']
  },
  module: {
    type: String,
    required: [true, 'Module is required']
  },
  order: {
    type: Number,
    required: [true, 'Lesson order is required']
  },
  
  // Lesson Type and Content
  type: {
    type: String,
    required: [true, 'Lesson type is required'],
    enum: [
      'video',
      'audio',
      'text',
      'quiz',
      'practice',
      'assignment',
      'live-session',
      'interactive'
    ]
  },
  
  // Content based on lesson type
  content: {
    // For video lessons
    video: {
      public_id: String,
      url: String,
      duration: Number, // in seconds
      quality: String,
      subtitles: [{
        language: String,
        url: String
      }],
      thumbnail: {
        public_id: String,
        url: String
      }
    },
    
    // For audio lessons
    audio: {
      public_id: String,
      url: String,
      duration: Number, // in seconds
      transcript: String
    },
    
    // For text lessons
    text: {
      content: String,
      readingTime: Number, // estimated reading time in minutes
      attachments: [{
        name: String,
        url: String,
        type: String,
        size: Number
      }]
    },
    
    // For quiz lessons
    quiz: {
      questions: [{
        question: {
          type: String,
          required: true
        },
        type: {
          type: String,
          enum: ['multiple-choice', 'true-false', 'fill-blank', 'essay'],
          required: true
        },
        options: [String], // for multiple choice
        correctAnswer: mongoose.Schema.Types.Mixed,
        explanation: String,
        points: {
          type: Number,
          default: 1
        },
        difficulty: {
          type: String,
          enum: ['easy', 'medium', 'hard'],
          default: 'medium'
        }
      }],
      timeLimit: Number, // in minutes
      passingScore: {
        type: Number,
        default: 70
      },
      allowRetakes: {
        type: Boolean,
        default: true
      },
      maxAttempts: {
        type: Number,
        default: 3
      }
    },
    
    // For practice lessons (PTE specific)
    practice: {
      section: {
        type: String,
        enum: ['speaking', 'writing', 'reading', 'listening'],
        required: true
      },
      taskType: {
        type: String,
        required: true
      },
      instructions: String,
      materials: [{
        type: String, // 'audio', 'image', 'text'
        url: String,
        content: String
      }],
      timeLimit: Number, // in seconds
      aiScoringEnabled: {
        type: Boolean,
        default: false
      },
      rubric: [{
        criteria: String,
        maxScore: Number,
        description: String
      }]
    },
    
    // For assignments
    assignment: {
      instructions: String,
      submissionType: {
        type: String,
        enum: ['file', 'text', 'audio', 'video'],
        required: true
      },
      maxFileSize: Number,
      allowedFileTypes: [String],
      dueDate: Date,
      rubric: [{
        criteria: String,
        maxScore: Number,
        description: String
      }]
    },
    
    // For live sessions
    liveSession: {
      scheduledAt: Date,
      duration: Number, // in minutes
      meetingLink: String,
      recordingUrl: String,
      maxParticipants: Number,
      agenda: String,
      materials: [{
        name: String,
        url: String
      }]
    }
  },
  
  // Lesson Settings
  isPreview: {
    type: Boolean,
    default: false
  },
  isRequired: {
    type: Boolean,
    default: true
  },
  prerequisites: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Lesson'
  }],
  
  // Progress Tracking
  completions: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    completedAt: {
      type: Date,
      default: Date.now
    },
    timeSpent: Number, // in seconds
    score: Number, // for quizzes and practices
    attempts: Number,
    feedback: String
  }],
  
  // Analytics
  analytics: {
    views: {
      type: Number,
      default: 0
    },
    completions: {
      type: Number,
      default: 0
    },
    averageTimeSpent: {
      type: Number,
      default: 0
    },
    averageScore: {
      type: Number,
      default: 0
    },
    dropoffRate: {
      type: Number,
      default: 0
    }
  },
  
  // Lesson Status
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft'
  },
  publishedAt: Date,
  
  // Additional Resources
  resources: [{
    title: String,
    description: String,
    type: {
      type: String,
      enum: ['pdf', 'link', 'video', 'audio', 'image']
    },
    url: String,
    isDownloadable: {
      type: Boolean,
      default: false
    }
  }],
  
  // Notes and Comments
  instructorNotes: String,
  studentNotes: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    note: String,
    timestamp: Number, // for video/audio lessons
    createdAt: {
      type: Date,
      default: Date.now
    }
  }]

}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
lessonSchema.index({ course: 1, order: 1 });
lessonSchema.index({ type: 1, status: 1 });
lessonSchema.index({ 'content.practice.section': 1 });
lessonSchema.index({ isPreview: 1 });

// Virtual for completion rate
lessonSchema.virtual('completionRate').get(function() {
  if (this.analytics.views === 0) return 0;
  return (this.analytics.completions / this.analytics.views) * 100;
});

// Virtual for estimated duration
lessonSchema.virtual('estimatedDuration').get(function() {
  switch (this.type) {
    case 'video':
      return this.content.video?.duration || 0;
    case 'audio':
      return this.content.audio?.duration || 0;
    case 'text':
      return (this.content.text?.readingTime || 5) * 60; // convert to seconds
    case 'quiz':
      return (this.content.quiz?.timeLimit || 10) * 60; // convert to seconds
    case 'practice':
      return this.content.practice?.timeLimit || 600; // 10 minutes default
    default:
      return 300; // 5 minutes default
  }
});

// Method to mark lesson as completed for a user
lessonSchema.methods.markCompleted = function(userId, timeSpent = 0, score = null) {
  const existingCompletion = this.completions.find(
    c => c.user.toString() === userId.toString()
  );
  
  if (existingCompletion) {
    existingCompletion.completedAt = new Date();
    existingCompletion.timeSpent = timeSpent;
    if (score !== null) existingCompletion.score = score;
    existingCompletion.attempts = (existingCompletion.attempts || 0) + 1;
  } else {
    this.completions.push({
      user: userId,
      timeSpent,
      score,
      attempts: 1
    });
    this.analytics.completions += 1;
  }
  
  // Update analytics
  this.updateAnalytics();
};

// Method to update analytics
lessonSchema.methods.updateAnalytics = function() {
  const completions = this.completions;
  
  if (completions.length > 0) {
    // Calculate average time spent
    const totalTime = completions.reduce((sum, c) => sum + (c.timeSpent || 0), 0);
    this.analytics.averageTimeSpent = totalTime / completions.length;
    
    // Calculate average score (for scored lessons)
    const scoredCompletions = completions.filter(c => c.score !== null && c.score !== undefined);
    if (scoredCompletions.length > 0) {
      const totalScore = scoredCompletions.reduce((sum, c) => sum + c.score, 0);
      this.analytics.averageScore = totalScore / scoredCompletions.length;
    }
  }
};

// Method to check if user can access lesson
lessonSchema.methods.canUserAccess = function(user, courseEnrollment) {
  // Check if lesson is preview
  if (this.isPreview) return true;
  
  // Check if user is enrolled in course
  if (!courseEnrollment) return false;
  
  // Check prerequisites
  if (this.prerequisites.length > 0) {
    const completedLessons = courseEnrollment.completedLessons.map(l => l.toString());
    const prerequisitesMet = this.prerequisites.every(
      prereq => completedLessons.includes(prereq.toString())
    );
    if (!prerequisitesMet) return false;
  }
  
  return true;
};

// Static method to get lessons by course
lessonSchema.statics.getLessonsByCourse = function(courseId, userId = null) {
  const query = { course: courseId, status: 'published' };
  
  return this.find(query)
    .sort({ order: 1 })
    .populate('prerequisites', 'title order')
    .lean();
};

export default mongoose.model('Lesson', lessonSchema);
