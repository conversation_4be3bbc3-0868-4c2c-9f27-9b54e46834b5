import { body, param, query } from 'express-validator';

// User validation rules
export const validateRegister = [
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('First name can only contain letters and spaces'),

  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Last name can only contain letters and spaces'),

  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),

  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),

  body('role')
    .optional()
    .isIn(['student', 'instructor', 'admin'])
    .withMessage('Role must be either student, instructor, or admin')
];

export const validateLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),

  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

export const validateChangePassword = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),

  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('New password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number')
];

export const validateForgotPassword = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address')
];

export const validateResetPassword = [
  param('resetToken')
    .isLength({ min: 64, max: 64 })
    .withMessage('Invalid reset token'),

  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number')
];

// Course validation rules
export const validateCreateCourse = [
  body('title')
    .trim()
    .isLength({ min: 5, max: 100 })
    .withMessage('Course title must be between 5 and 100 characters'),

  body('description')
    .trim()
    .isLength({ min: 20, max: 1000 })
    .withMessage('Course description must be between 20 and 1000 characters'),

  body('shortDescription')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Short description cannot exceed 200 characters'),

  body('category')
    .isIn(['speaking', 'writing', 'reading', 'listening', 'mock-tests', 'comprehensive'])
    .withMessage('Invalid course category'),

  body('level')
    .isIn(['beginner', 'intermediate', 'advanced', 'all'])
    .withMessage('Invalid course level'),

  body('pricing.type')
    .isIn(['free', 'paid', 'subscription'])
    .withMessage('Invalid pricing type'),

  body('pricing.price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),

  body('pricing.currency')
    .optional()
    .isLength({ min: 3, max: 3 })
    .withMessage('Currency must be a 3-letter code'),

  body('features')
    .optional()
    .isArray()
    .withMessage('Features must be an array'),

  body('learningOutcomes')
    .optional()
    .isArray()
    .withMessage('Learning outcomes must be an array'),

  body('prerequisites')
    .optional()
    .isArray()
    .withMessage('Prerequisites must be an array')
];

export const validateUpdateCourse = [
  param('id')
    .isMongoId()
    .withMessage('Invalid course ID'),

  body('title')
    .optional()
    .trim()
    .isLength({ min: 5, max: 100 })
    .withMessage('Course title must be between 5 and 100 characters'),

  body('description')
    .optional()
    .trim()
    .isLength({ min: 20, max: 1000 })
    .withMessage('Course description must be between 20 and 1000 characters'),

  body('category')
    .optional()
    .isIn(['speaking', 'writing', 'reading', 'listening', 'mock-tests', 'comprehensive'])
    .withMessage('Invalid course category'),

  body('level')
    .optional()
    .isIn(['beginner', 'intermediate', 'advanced', 'all'])
    .withMessage('Invalid course level'),

  body('pricing.price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number')
];

// Lesson validation rules
export const validateCreateLesson = [
  body('title')
    .trim()
    .isLength({ min: 3, max: 100 })
    .withMessage('Lesson title must be between 3 and 100 characters'),

  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),

  body('course')
    .isMongoId()
    .withMessage('Invalid course ID'),

  body('module')
    .trim()
    .notEmpty()
    .withMessage('Module is required'),

  body('order')
    .isInt({ min: 1 })
    .withMessage('Order must be a positive integer'),

  body('type')
    .isIn(['video', 'audio', 'text', 'quiz', 'practice', 'assignment', 'live-session', 'interactive'])
    .withMessage('Invalid lesson type')
];

// Review validation rules
export const validateAddReview = [
  param('id')
    .isMongoId()
    .withMessage('Invalid course ID'),

  body('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),

  body('comment')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Comment cannot exceed 500 characters')
];

// Query validation rules
export const validateCourseQuery = [
  query('category')
    .optional()
    .isIn(['all', 'speaking', 'writing', 'reading', 'listening', 'mock-tests', 'comprehensive'])
    .withMessage('Invalid category filter'),

  query('level')
    .optional()
    .isIn(['all', 'beginner', 'intermediate', 'advanced'])
    .withMessage('Invalid level filter'),

  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),

  query('priceMin')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Minimum price must be a positive number'),

  query('priceMax')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Maximum price must be a positive number'),

  query('rating')
    .optional()
    .isFloat({ min: 0, max: 5 })
    .withMessage('Rating must be between 0 and 5'),

  query('sort')
    .optional()
    .isIn([
      'createdAt', '-createdAt',
      'title', '-title',
      'price', '-price',
      'rating', '-rating',
      'enrollments', '-enrollments'
    ])
    .withMessage('Invalid sort parameter')
];

// User profile validation rules
export const validateUpdateProfile = [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),

  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),

  body('phoneNumber')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),

  body('country')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Country must be between 2 and 50 characters'),

  body('targetScore')
    .optional()
    .isInt({ min: 10, max: 90 })
    .withMessage('Target score must be between 10 and 90'),

  body('currentLevel')
    .optional()
    .isIn(['beginner', 'intermediate', 'advanced'])
    .withMessage('Invalid current level'),

  body('dateOfBirth')
    .optional()
    .isISO8601()
    .withMessage('Please provide a valid date of birth')
];

// MongoDB ObjectId validation
export const validateObjectId = (paramName = 'id') => [
  param(paramName)
    .isMongoId()
    .withMessage(`Invalid ${paramName}`)
];

// File upload validation
export const validateFileUpload = [
  body('fileType')
    .optional()
    .isIn(['image', 'video', 'audio', 'document'])
    .withMessage('Invalid file type'),

  body('purpose')
    .optional()
    .isIn(['avatar', 'course-thumbnail', 'lesson-content', 'assignment'])
    .withMessage('Invalid upload purpose')
];

// Pagination validation
export const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer')
    .toInt(),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100')
    .toInt()
];

// Search validation
export const validateSearch = [
  query('q')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search query must be between 1 and 100 characters'),

  query('type')
    .optional()
    .isIn(['courses', 'lessons', 'users', 'all'])
    .withMessage('Invalid search type')
];

// User management validation rules
export const validateUserQuery = [
  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search query must be between 1 and 100 characters'),

  query('role')
    .optional()
    .isIn(['all', 'student', 'instructor', 'admin'])
    .withMessage('Invalid role filter'),

  query('status')
    .optional()
    .isIn(['active', 'blocked', 'inactive'])
    .withMessage('Invalid status filter'),

  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100')
];

export const validateUpdateUser = [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),

  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),

  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),

  body('role')
    .optional()
    .isIn(['student', 'instructor', 'admin'])
    .withMessage('Role must be student, instructor, or admin'),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),

  body('phoneNumber')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),

  body('country')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Country must be between 2 and 50 characters'),

  body('targetScore')
    .optional()
    .isInt({ min: 10, max: 90 })
    .withMessage('Target score must be between 10 and 90'),

  body('currentLevel')
    .optional()
    .isIn(['beginner', 'intermediate', 'advanced'])
    .withMessage('Invalid current level')
];

export const validateBlockUser = [
  body('reason')
    .optional()
    .trim()
    .isLength({ min: 1, max: 500 })
    .withMessage('Block reason must be between 1 and 500 characters')
];
