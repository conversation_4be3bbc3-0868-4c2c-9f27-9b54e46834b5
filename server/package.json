{"name": "pte-proficiency-backend", "version": "1.0.0", "description": "Backend API for PTE Proficiency Learning Platform", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "seed": "node scripts/seedDatabase.js"}, "keywords": ["pte", "education", "api", "nodejs", "express", "mongodb"], "author": "PTE Proficiency Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "compression": "^1.7.4", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "passport": "^0.7.0", "passport-apple": "^2.0.2", "passport-google-oauth20": "^2.0.0", "passport-microsoft": "^1.0.0", "stripe": "^14.7.0"}, "devDependencies": {"@types/jest": "^29.5.8", "jest": "^29.7.0", "mongodb-memory-server": "^10.1.4", "nodemon": "^3.1.10", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}