import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import dotenv from 'dotenv';

dotenv.config();

let mongoServer = null;

const connectDB = async () => {
  try {
    // Check if we have a real MongoDB URI
    const mongoURI = process.env.NODE_ENV === 'test'
      ? process.env.MONGODB_TEST_URI
      : process.env.MONGODB_URI;

    let connectionString = mongoURI;

    // If no MongoDB URI is provided, use in-memory MongoDB
    if (!mongoURI) {
      console.log('🚀 Starting in-memory MongoDB for development...');
      console.log('⚠️  Note: Data will be lost when server restarts. Create courses after each restart.');

      mongoServer = await MongoMemoryServer.create({
        instance: {
          port: 27017, // Use default MongoDB port
          dbName: 'pte-proficiency-dev'
        }
      });

      connectionString = mongoServer.getUri();
      console.log(`📦 In-memory MongoDB started at: ${connectionString}`);
    }

    const conn = await mongoose.connect(connectionString, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log(`✅ MongoDB Connected: ${conn.connection.host}`);

    // Handle connection events
    mongoose.connection.on('error', (err) => {
      console.error('MongoDB connection error:', err);
    });

    mongoose.connection.on('disconnected', () => {
      console.log('MongoDB disconnected');
    });

    // Graceful shutdown
    const gracefulShutdown = async () => {
      try {
        await mongoose.connection.close();
        console.log('MongoDB connection closed');

        if (mongoServer) {
          await mongoServer.stop();
          console.log('In-memory MongoDB stopped');
        }

        process.exit(0);
      } catch (error) {
        console.error('Error during shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGINT', gracefulShutdown);
    process.on('SIGTERM', gracefulShutdown);

  } catch (error) {
    console.error('Database connection failed:', error.message);
    process.exit(1);
  }
};

// Function to seed initial data for development
const seedDevData = async () => {
  try {
    // Import models
    const User = (await import('../models/User.js')).default;
    const Course = (await import('../models/Course.js')).default;

    // Check if data already exists
    const userCount = await User.countDocuments();
    if (userCount > 0) {
      console.log('📊 Development data already exists, skipping seed...');
      return;
    }

    console.log('🌱 Seeding development data...');

    // Create admin user
    const adminUser = await User.create({
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      password: 'Admin123!',
      role: 'admin',
      isEmailVerified: true,
      isActive: true
    });

    // Create instructor
    const instructor = await User.create({
      firstName: 'Dr. Sarah',
      lastName: 'Johnson',
      email: '<EMAIL>',
      password: 'Instructor123!',
      role: 'instructor',
      isEmailVerified: true,
      isActive: true
    });

    // Create student
    const student = await User.create({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      password: 'Student123!',
      role: 'student',
      isEmailVerified: true,
      isActive: true,
      targetScore: 79,
      currentLevel: 'intermediate'
    });

    // Create sample courses
    const courses = await Course.create([
      {
        title: 'PTE Academic Speaking Mastery',
        description: 'Master all speaking tasks with AI-powered feedback and scoring.',
        shortDescription: 'Master all PTE speaking tasks with AI feedback',
        category: 'speaking',
        level: 'intermediate',
        tags: ['speaking', 'pronunciation', 'fluency', 'ai-scoring'],
        instructor: instructor._id,
        pricing: {
          type: 'paid',
          price: 99,
          originalPrice: 149,
          currency: 'USD',
          discountPercentage: 33
        },
        features: ['AI Scoring', 'Live Sessions', 'Practice Tests'],
        learningOutcomes: ['Improve pronunciation', 'Master speaking tasks'],
        isPublished: true,
        status: 'published',
        publishedAt: new Date()
      },
      {
        title: 'Free PTE Introduction Course',
        description: 'Get started with PTE Academic preparation.',
        shortDescription: 'Free introduction to PTE Academic',
        category: 'comprehensive',
        level: 'beginner',
        tags: ['introduction', 'free', 'basics'],
        instructor: instructor._id,
        pricing: {
          type: 'free',
          price: 0,
          currency: 'USD'
        },
        features: ['Test Overview', 'Basic Strategies'],
        learningOutcomes: ['Understand PTE format', 'Learn basic strategies'],
        isPublished: true,
        status: 'published',
        publishedAt: new Date()
      }
    ]);

    console.log(`✅ Created ${courses.length} sample courses`);
    console.log('👤 Development users created:');
    console.log(`   Admin: ${adminUser.email} / Admin123!`);
    console.log(`   Instructor: ${instructor.email} / Instructor123!`);
    console.log(`   Student: ${student.email} / Student123!`);

  } catch (error) {
    console.error('Error seeding development data:', error);
  }
};

export { connectDB, seedDevData };
