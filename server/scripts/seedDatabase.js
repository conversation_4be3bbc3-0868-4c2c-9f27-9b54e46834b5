import dotenv from 'dotenv';
import connectDB from '../config/database.js';
import User from '../models/User.js';
import Course from '../models/Course.js';
import Lesson from '../models/Lesson.js';

// Load environment variables
dotenv.config();

// Connect to database
connectDB();

const seedUsers = async () => {
  console.log('Seeding users...');
  
  const users = [
    {
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      password: 'Admin123!',
      role: 'admin',
      isEmailVerified: true,
      isActive: true
    },
    {
      firstName: 'Dr. <PERSON>',
      lastName: '<PERSON>',
      email: '<EMAIL>',
      password: 'Instructor123!',
      role: 'instructor',
      isEmailVerified: true,
      isActive: true
    },
    {
      firstName: 'Prof. <PERSON>',
      lastName: 'Chen',
      email: 'micha<PERSON>.<EMAIL>',
      password: 'Instructor123!',
      role: 'instructor',
      isEmailVerified: true,
      isActive: true
    },
    {
      firstName: 'Dr. <PERSON>',
      lastName: '<PERSON>',
      email: '<EMAIL>',
      password: 'Instructor123!',
      role: 'instructor',
      isEmailVerified: true,
      isActive: true
    },
    {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      password: 'Student123!',
      role: 'student',
      isEmailVerified: true,
      isActive: true,
      targetScore: 79,
      currentLevel: 'intermediate'
    }
  ];

  const createdUsers = [];
  for (const userData of users) {
    const existingUser = await User.findOne({ email: userData.email });
    if (!existingUser) {
      const user = await User.create(userData);
      createdUsers.push(user);
      console.log(`Created user: ${user.email}`);
    } else {
      createdUsers.push(existingUser);
      console.log(`User already exists: ${userData.email}`);
    }
  }

  return createdUsers;
};

const seedCourses = async (users) => {
  console.log('Seeding courses...');
  
  const instructors = users.filter(user => user.role === 'instructor');
  
  const courses = [
    {
      title: 'PTE Academic Speaking Mastery',
      description: 'Master all speaking tasks with AI-powered feedback and scoring. This comprehensive course covers Read Aloud, Repeat Sentence, Describe Image, Re-tell Lecture, and Answer Short Question tasks.',
      shortDescription: 'Master all PTE speaking tasks with AI feedback',
      category: 'speaking',
      level: 'intermediate',
      tags: ['speaking', 'pronunciation', 'fluency', 'ai-scoring'],
      instructor: instructors[0]._id,
      pricing: {
        type: 'paid',
        price: 99,
        originalPrice: 149,
        currency: 'USD',
        discountPercentage: 33
      },
      features: ['AI Scoring', 'Live Sessions', 'Practice Tests', 'Pronunciation Training'],
      learningOutcomes: [
        'Improve pronunciation and fluency',
        'Master all speaking task types',
        'Achieve target speaking score',
        'Build confidence in speaking'
      ],
      isPublished: true,
      isVip: true,
      status: 'published',
      publishedAt: new Date()
    },
    {
      title: 'Writing - Grammar Training',
      description: 'Improve your writing skills with comprehensive grammar training. Learn to write effective essays and summaries for PTE Academic.',
      shortDescription: 'Comprehensive grammar training for PTE writing',
      category: 'writing',
      level: 'beginner',
      tags: ['writing', 'grammar', 'essay', 'summary'],
      instructor: instructors[1]._id,
      pricing: {
        type: 'paid',
        price: 79,
        originalPrice: 99,
        currency: 'USD',
        discountPercentage: 20
      },
      features: ['Grammar Checker', 'Essay Templates', 'Feedback', 'Writing Practice'],
      learningOutcomes: [
        'Master PTE writing tasks',
        'Improve grammar accuracy',
        'Learn essay structure',
        'Enhance vocabulary usage'
      ],
      isPublished: true,
      status: 'published',
      publishedAt: new Date()
    },
    {
      title: 'Reading Comprehension Pro',
      description: 'Advanced reading strategies for PTE Academic success. Master multiple choice, fill in the blanks, and re-order paragraphs.',
      shortDescription: 'Advanced reading strategies for PTE success',
      category: 'reading',
      level: 'advanced',
      tags: ['reading', 'comprehension', 'strategy', 'speed-reading'],
      instructor: instructors[2]._id,
      pricing: {
        type: 'paid',
        price: 89,
        originalPrice: 119,
        currency: 'USD',
        discountPercentage: 25
      },
      features: ['Speed Reading', 'Comprehension Tests', 'Vocabulary Builder', 'Strategy Training'],
      learningOutcomes: [
        'Improve reading speed',
        'Enhance comprehension skills',
        'Master reading task types',
        'Build academic vocabulary'
      ],
      isPublished: true,
      status: 'published',
      publishedAt: new Date()
    },
    {
      title: 'Listening Skills Enhancement',
      description: 'Develop superior listening skills for all PTE tasks. Practice with authentic materials and improve note-taking techniques.',
      shortDescription: 'Develop superior listening skills for PTE',
      category: 'listening',
      level: 'intermediate',
      tags: ['listening', 'note-taking', 'accent-training', 'comprehension'],
      instructor: instructors[0]._id,
      pricing: {
        type: 'paid',
        price: 85,
        originalPrice: 110,
        currency: 'USD',
        discountPercentage: 23
      },
      features: ['Audio Practice', 'Note Taking', 'Accent Training', 'Listening Strategies'],
      learningOutcomes: [
        'Improve listening accuracy',
        'Master note-taking skills',
        'Understand different accents',
        'Excel in listening tasks'
      ],
      isPublished: true,
      isVip: true,
      status: 'published',
      publishedAt: new Date()
    },
    {
      title: 'Complete PTE Mock Tests',
      description: 'Full-length practice tests with detailed analysis and scoring. Simulate real exam conditions.',
      shortDescription: 'Full-length practice tests with detailed analysis',
      category: 'mock-tests',
      level: 'all',
      tags: ['mock-test', 'practice', 'scoring', 'analysis'],
      instructor: instructors[1]._id,
      pricing: {
        type: 'paid',
        price: 59,
        originalPrice: 79,
        currency: 'USD',
        discountPercentage: 25
      },
      features: ['Timed Tests', 'Score Analysis', 'Performance Tracking', 'Detailed Feedback'],
      learningOutcomes: [
        'Experience real exam conditions',
        'Identify strengths and weaknesses',
        'Track progress over time',
        'Build exam confidence'
      ],
      isPublished: true,
      status: 'published',
      publishedAt: new Date()
    },
    {
      title: 'Free PTE Introduction Course',
      description: 'Get started with PTE Academic preparation. Learn about the test format and basic strategies.',
      shortDescription: 'Free introduction to PTE Academic',
      category: 'comprehensive',
      level: 'beginner',
      tags: ['introduction', 'free', 'basics', 'overview'],
      instructor: instructors[2]._id,
      pricing: {
        type: 'free',
        price: 0,
        currency: 'USD'
      },
      features: ['Test Overview', 'Basic Strategies', 'Sample Questions', 'Study Plan'],
      learningOutcomes: [
        'Understand PTE test format',
        'Learn basic strategies',
        'Get familiar with question types',
        'Create study plan'
      ],
      isPublished: true,
      status: 'published',
      publishedAt: new Date()
    }
  ];

  const createdCourses = [];
  for (const courseData of courses) {
    const existingCourse = await Course.findOne({ title: courseData.title });
    if (!existingCourse) {
      const course = await Course.create(courseData);
      createdCourses.push(course);
      console.log(`Created course: ${course.title}`);
    } else {
      createdCourses.push(existingCourse);
      console.log(`Course already exists: ${courseData.title}`);
    }
  }

  return createdCourses;
};

const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');
    
    // Clear existing data (optional - comment out if you want to keep existing data)
    // await User.deleteMany({});
    // await Course.deleteMany({});
    // await Lesson.deleteMany({});
    // console.log('Cleared existing data');

    // Seed users
    const users = await seedUsers();
    
    // Seed courses
    const courses = await seedCourses(users);

    console.log('✅ Database seeding completed successfully!');
    console.log(`Created ${users.length} users and ${courses.length} courses`);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
};

// Run seeding
seedDatabase();
