import User from '../models/User.js';
import { validationResult } from 'express-validator';

// @desc    Get all users (Admin only)
// @route   GET /api/v1/users
// @access  Private/Admin
export const getUsers = async (req, res) => {
  try {
    const {
      search,
      role,
      status,
      sort = '-createdAt',
      page = 1,
      limit = 20
    } = req.query;

    // Build query
    const query = {};

    // Search by name or email
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    // Filter by role
    if (role && role !== 'all') {
      query.role = role;
    }

    // Filter by status
    if (status) {
      if (status === 'active') {
        query.isActive = true;
        query.isBlocked = false;
      } else if (status === 'blocked') {
        query.isBlocked = true;
      } else if (status === 'inactive') {
        query.isActive = false;
      }
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const users = await User.find(query)
      .select('-password -resetPasswordToken -emailVerificationToken')
      .sort(sort)
      .skip(skip)
      .limit(Number(limit));

    // Get total count for pagination
    const total = await User.countDocuments(query);

    // Get stats
    const stats = await User.aggregate([
      {
        $group: {
          _id: null,
          totalUsers: { $sum: 1 },
          activeUsers: {
            $sum: {
              $cond: [{ $and: [{ $eq: ['$isActive', true] }, { $eq: ['$isBlocked', false] }] }, 1, 0]
            }
          },
          blockedUsers: { $sum: { $cond: ['$isBlocked', 1, 0] } },
          students: { $sum: { $cond: [{ $eq: ['$role', 'student'] }, 1, 0] } },
          instructors: { $sum: { $cond: [{ $eq: ['$role', 'instructor'] }, 1, 0] } },
          admins: { $sum: { $cond: [{ $eq: ['$role', 'admin'] }, 1, 0] } }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / limit)
        },
        stats: stats[0] || {
          totalUsers: 0,
          activeUsers: 0,
          blockedUsers: 0,
          students: 0,
          instructors: 0,
          admins: 0
        }
      }
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching users'
    });
  }
};

// @desc    Get single user (Admin only)
// @route   GET /api/v1/users/:id
// @access  Private/Admin
export const getUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id)
      .select('-password -resetPasswordToken -emailVerificationToken')
      .populate('enrolledCourses.course', 'title category level');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: { user }
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching user'
    });
  }
};

// @desc    Update user (Admin only)
// @route   PUT /api/v1/users/:id
// @access  Private/Admin
export const updateUser = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Prevent admin from demoting themselves
    if (req.user._id.toString() === req.params.id && req.body.role && req.body.role !== 'admin') {
      return res.status(400).json({
        success: false,
        message: 'You cannot change your own admin role'
      });
    }

    const updatedUser = await User.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    ).select('-password -resetPasswordToken -emailVerificationToken');

    res.json({
      success: true,
      message: 'User updated successfully',
      data: { user: updatedUser }
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error updating user'
    });
  }
};

// @desc    Block/Unblock user (Admin only)
// @route   PUT /api/v1/users/:id/block
// @access  Private/Admin
export const toggleUserBlock = async (req, res) => {
  try {
    const { reason } = req.body;
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Prevent admin from blocking themselves
    if (req.user._id.toString() === req.params.id) {
      return res.status(400).json({
        success: false,
        message: 'You cannot block yourself'
      });
    }

    // Toggle block status
    user.isBlocked = !user.isBlocked;
    if (user.isBlocked) {
      user.blockedReason = reason || 'No reason provided';
      user.blockedAt = new Date();
    } else {
      user.blockedReason = undefined;
      user.blockedAt = undefined;
    }

    await user.save();

    res.json({
      success: true,
      message: `User ${user.isBlocked ? 'blocked' : 'unblocked'} successfully`,
      data: { 
        user: {
          id: user._id,
          isBlocked: user.isBlocked,
          blockedReason: user.blockedReason,
          blockedAt: user.blockedAt
        }
      }
    });
  } catch (error) {
    console.error('Toggle user block error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error updating user status'
    });
  }
};

// @desc    Delete user (Admin only)
// @route   DELETE /api/v1/users/:id
// @access  Private/Admin
export const deleteUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Prevent admin from deleting themselves
    if (req.user._id.toString() === req.params.id) {
      return res.status(400).json({
        success: false,
        message: 'You cannot delete yourself'
      });
    }

    await User.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error deleting user'
    });
  }
};

// @desc    Get user activity/stats (Admin only)
// @route   GET /api/v1/users/:id/activity
// @access  Private/Admin
export const getUserActivity = async (req, res) => {
  try {
    const user = await User.findById(req.params.id)
      .select('loginCount lastLoginAt studyStreak enrolledCourses testResults')
      .populate('enrolledCourses.course', 'title category');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Calculate additional stats
    const totalCourses = user.enrolledCourses?.length || 0;
    const completedCourses = user.enrolledCourses?.filter(enrollment => 
      enrollment.progress === 100
    ).length || 0;
    const totalTests = user.testResults?.length || 0;
    const averageScore = totalTests > 0 
      ? user.testResults.reduce((sum, test) => sum + (test.score || 0), 0) / totalTests 
      : 0;

    const activity = {
      loginStats: {
        totalLogins: user.loginCount || 0,
        lastLogin: user.lastLoginAt
      },
      studyStats: {
        currentStreak: user.studyStreak?.current || 0,
        longestStreak: user.studyStreak?.longest || 0,
        lastStudyDate: user.studyStreak?.lastStudyDate
      },
      courseStats: {
        totalEnrolled: totalCourses,
        completed: completedCourses,
        inProgress: totalCourses - completedCourses
      },
      testStats: {
        totalTests,
        averageScore: Math.round(averageScore * 100) / 100
      }
    };

    res.json({
      success: true,
      data: { activity }
    });
  } catch (error) {
    console.error('Get user activity error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching user activity'
    });
  }
};
