import Course from '../models/Course.js';
import User from '../models/User.js';
import { validationResult } from 'express-validator';

// @desc    Get all courses
// @route   GET /api/v1/courses
// @access  Public
export const getCourses = async (req, res) => {
  try {
    const {
      category,
      level,
      search,
      sort = '-createdAt',
      page = 1,
      limit = 12,
      priceMin,
      priceMax,
      rating,
      instructor
    } = req.query;

    // Build query - admins can see all courses, others only published
    const query = {};

    // Only filter by published status if user is not admin
    if (!req.user || req.user.role !== 'admin') {
      query.isPublished = true;
      query.status = 'published';
    }

    if (category && category !== 'all') {
      query.category = category;
    }

    if (level && level !== 'all') {
      query.level = { $in: [level, 'all'] };
    }

    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    if (priceMin !== undefined || priceMax !== undefined) {
      query['pricing.price'] = {};
      if (priceMin !== undefined) query['pricing.price'].$gte = Number(priceMin);
      if (priceMax !== undefined) query['pricing.price'].$lte = Number(priceMax);
    }

    if (rating) {
      query['stats.averageRating'] = { $gte: Number(rating) };
    }

    if (instructor) {
      query.instructor = instructor;
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const courses = await Course.find(query)
      .populate('instructor', 'firstName lastName avatar')
      .select('-enrollments -reviews')
      .sort(sort)
      .skip(skip)
      .limit(Number(limit));

    // Get total count for pagination
    const total = await Course.countDocuments(query);

    res.json({
      success: true,
      data: {
        courses,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get courses error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching courses'
    });
  }
};

// @desc    Get single course
// @route   GET /api/v1/courses/:id
// @access  Public
export const getCourse = async (req, res) => {
  try {
    const course = await Course.findById(req.params.id)
      .populate('instructor', 'firstName lastName avatar')
      .populate('coInstructors', 'firstName lastName avatar')
      .populate({
        path: 'lessons',
        match: { status: 'published' },
        select: 'title description type order isPreview estimatedDuration',
        options: { sort: { order: 1 } }
      })
      .populate({
        path: 'reviews.user',
        select: 'firstName lastName avatar'
      });

    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }

    // Check if course is published (unless user is instructor/admin)
    if (!course.isPublished &&
        (!req.user ||
         (req.user.role !== 'admin' &&
          course.instructor._id.toString() !== req.user._id.toString()))) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }

    // Increment view count
    course.analytics.views += 1;
    await course.save();

    // Check if user is enrolled (if authenticated)
    let isEnrolled = false;
    let enrollment = null;
    if (req.user) {
      enrollment = req.user.enrolledCourses.find(
        e => e.course.toString() === course._id.toString()
      );
      isEnrolled = !!enrollment;
    }

    res.json({
      success: true,
      data: {
        course,
        isEnrolled,
        enrollment
      }
    });
  } catch (error) {
    console.error('Get course error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching course'
    });
  }
};

// @desc    Create course
// @route   POST /api/v1/courses
// @access  Private (Instructor/Admin)
export const createCourse = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    // Set instructor to current user
    req.body.instructor = req.user._id;

    const course = await Course.create(req.body);

    // Populate instructor information for the response
    await course.populate('instructor', 'firstName lastName avatar');

    res.status(201).json({
      success: true,
      message: 'Course created successfully',
      data: { course }
    });
  } catch (error) {
    console.error('Create course error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error creating course'
    });
  }
};

// @desc    Update course
// @route   PUT /api/v1/courses/:id
// @access  Private (Instructor/Admin)
export const updateCourse = async (req, res) => {
  try {
    const course = await Course.findById(req.params.id);

    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }

    // Check ownership (instructor can only update their own courses)
    if (req.user.role !== 'admin' &&
        course.instructor.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this course'
      });
    }

    const updatedCourse = await Course.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    ).populate('instructor', 'firstName lastName avatar');

    res.json({
      success: true,
      message: 'Course updated successfully',
      data: { course: updatedCourse }
    });
  } catch (error) {
    console.error('Update course error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error updating course'
    });
  }
};

// @desc    Delete course
// @route   DELETE /api/v1/courses/:id
// @access  Private (Instructor/Admin)
export const deleteCourse = async (req, res) => {
  try {
    const course = await Course.findById(req.params.id);

    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }

    // Check ownership
    if (req.user.role !== 'admin' &&
        course.instructor.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete this course'
      });
    }

    // Soft delete - archive instead of removing
    course.status = 'archived';
    course.archivedAt = new Date();
    await course.save();

    res.json({
      success: true,
      message: 'Course archived successfully'
    });
  } catch (error) {
    console.error('Delete course error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error deleting course'
    });
  }
};

// @desc    Enroll in course
// @route   POST /api/v1/courses/:id/enroll
// @access  Private
export const enrollInCourse = async (req, res) => {
  try {
    const course = await Course.findById(req.params.id);

    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }

    if (!course.isPublished) {
      return res.status(400).json({
        success: false,
        message: 'Course is not available for enrollment'
      });
    }

    // Check if already enrolled
    const existingEnrollment = req.user.enrolledCourses.find(
      e => e.course.toString() === course._id.toString()
    );

    if (existingEnrollment) {
      return res.status(400).json({
        success: false,
        message: 'Already enrolled in this course'
      });
    }

    // Check subscription requirements for paid courses
    if (course.pricing.type === 'paid' && course.pricing.price > 0) {
      // TODO: Implement payment processing with Stripe
      return res.status(400).json({
        success: false,
        message: 'Payment processing not implemented yet'
      });
    }

    // Add enrollment to user
    req.user.enrolledCourses.push({
      course: course._id,
      enrolledAt: new Date(),
      progress: 0
    });

    // Add enrollment to course
    course.enrollments.push({
      user: req.user._id,
      enrolledAt: new Date()
    });

    // Update course stats
    course.stats.totalEnrollments += 1;
    course.stats.activeEnrollments += 1;

    await Promise.all([req.user.save(), course.save()]);

    res.json({
      success: true,
      message: 'Successfully enrolled in course',
      data: {
        enrollment: req.user.enrolledCourses[req.user.enrolledCourses.length - 1]
      }
    });
  } catch (error) {
    console.error('Enroll in course error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error enrolling in course'
    });
  }
};

// @desc    Get user's enrolled courses
// @route   GET /api/v1/courses/my-courses
// @access  Private
export const getMyEnrolledCourses = async (req, res) => {
  try {
    const user = await User.findById(req.user._id)
      .populate({
        path: 'enrolledCourses.course',
        select: 'title description thumbnail category level instructor stats pricing',
        populate: {
          path: 'instructor',
          select: 'firstName lastName avatar'
        }
      });

    const enrolledCourses = user.enrolledCourses.map(enrollment => ({
      ...enrollment.toObject(),
      course: enrollment.course
    }));

    res.json({
      success: true,
      data: { enrolledCourses }
    });
  } catch (error) {
    console.error('Get enrolled courses error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching enrolled courses'
    });
  }
};

// @desc    Add course review
// @route   POST /api/v1/courses/:id/reviews
// @access  Private
export const addCourseReview = async (req, res) => {
  try {
    const { rating, comment } = req.body;
    const course = await Course.findById(req.params.id);

    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }

    // Check if user is enrolled
    const enrollment = req.user.enrolledCourses.find(
      e => e.course.toString() === course._id.toString()
    );

    if (!enrollment) {
      return res.status(400).json({
        success: false,
        message: 'You must be enrolled to review this course'
      });
    }

    // Check if user already reviewed
    const existingReview = course.reviews.find(
      r => r.user.toString() === req.user._id.toString()
    );

    if (existingReview) {
      return res.status(400).json({
        success: false,
        message: 'You have already reviewed this course'
      });
    }

    // Add review
    course.reviews.push({
      user: req.user._id,
      rating,
      comment,
      isVerified: enrollment.progress > 50 // Verified if more than 50% complete
    });

    // Update average rating
    course.calculateAverageRating();
    await course.save();

    res.status(201).json({
      success: true,
      message: 'Review added successfully'
    });
  } catch (error) {
    console.error('Add review error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error adding review'
    });
  }
};
