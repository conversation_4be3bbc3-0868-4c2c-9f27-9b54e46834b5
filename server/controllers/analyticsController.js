import User from '../models/User.js';
import Course from '../models/Course.js';
import Lesson from '../models/Lesson.js';
import mongoose from 'mongoose';

// @desc    Get dashboard analytics overview
// @route   GET /api/v1/analytics/dashboard
// @access  Private/Admin
export const getDashboardAnalytics = async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;

    // Calculate date range based on timeframe
    const now = new Date();
    let startDate;

    switch (timeframe) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // Parallel execution of analytics queries
    const [
      userStats,
      courseStats,
      enrollmentStats,
      revenueStats,
      activityStats
    ] = await Promise.all([
      getUserStats(startDate),
      getCourseStats(startDate),
      getEnrollmentStats(startDate),
      getRevenueStats(startDate),
      getActivityStats(startDate)
    ]);

    res.json({
      success: true,
      data: {
        timeframe,
        period: {
          start: startDate,
          end: now
        },
        overview: {
          users: userStats,
          courses: courseStats,
          enrollments: enrollmentStats,
          revenue: revenueStats,
          activity: activityStats
        }
      }
    });
  } catch (error) {
    console.error('Dashboard analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching dashboard analytics'
    });
  }
};

// Helper function to get user statistics
const getUserStats = async (startDate) => {
  const [totalUsers, newUsers, activeUsers, usersByRole] = await Promise.all([
    User.countDocuments({ isActive: true, isBlocked: false }),
    User.countDocuments({
      createdAt: { $gte: startDate },
      isActive: true,
      isBlocked: false
    }),
    User.countDocuments({
      lastLoginAt: { $gte: startDate },
      isActive: true,
      isBlocked: false
    }),
    User.aggregate([
      {
        $match: { isActive: true, isBlocked: false }
      },
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 }
        }
      }
    ])
  ]);

  const roleDistribution = usersByRole.reduce((acc, item) => {
    acc[item._id] = item.count;
    return acc;
  }, { student: 0, instructor: 0, admin: 0 });

  return {
    total: totalUsers,
    new: newUsers,
    active: activeUsers,
    roleDistribution
  };
};

// Helper function to get course statistics
const getCourseStats = async (startDate) => {
  const [totalCourses, newCourses, publishedCourses, coursesByCategory] = await Promise.all([
    Course.countDocuments({ status: { $ne: 'archived' } }),
    Course.countDocuments({
      createdAt: { $gte: startDate },
      status: { $ne: 'archived' }
    }),
    Course.countDocuments({ status: 'published', isPublished: true }),
    Course.aggregate([
      {
        $match: { status: 'published', isPublished: true }
      },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          totalEnrollments: { $sum: '$stats.totalEnrollments' },
          averageRating: { $avg: '$stats.averageRating' }
        }
      }
    ])
  ]);

  return {
    total: totalCourses,
    new: newCourses,
    published: publishedCourses,
    categoryDistribution: coursesByCategory
  };
};

// Helper function to get enrollment statistics
const getEnrollmentStats = async (startDate) => {
  const enrollmentData = await Course.aggregate([
    {
      $unwind: '$enrollments'
    },
    {
      $match: {
        'enrollments.enrolledAt': { $gte: startDate }
      }
    },
    {
      $group: {
        _id: null,
        totalEnrollments: { $sum: 1 },
        completedEnrollments: {
          $sum: {
            $cond: [{ $eq: ['$enrollments.progress', 100] }, 1, 0]
          }
        },
        averageProgress: { $avg: '$enrollments.progress' }
      }
    }
  ]);

  const stats = enrollmentData[0] || {
    totalEnrollments: 0,
    completedEnrollments: 0,
    averageProgress: 0
  };

  return {
    total: stats.totalEnrollments,
    completed: stats.completedEnrollments,
    inProgress: stats.totalEnrollments - stats.completedEnrollments,
    averageProgress: Math.round(stats.averageProgress * 100) / 100,
    completionRate: stats.totalEnrollments > 0
      ? Math.round((stats.completedEnrollments / stats.totalEnrollments) * 100 * 100) / 100
      : 0
  };
};

// Helper function to get revenue statistics
const getRevenueStats = async (startDate) => {
  const revenueData = await Course.aggregate([
    {
      $match: {
        'stats.totalRevenue': { $gt: 0 }
      }
    },
    {
      $group: {
        _id: null,
        totalRevenue: { $sum: '$stats.totalRevenue' },
        averageRevenue: { $avg: '$stats.totalRevenue' },
        courseCount: { $sum: 1 }
      }
    }
  ]);

  const stats = revenueData[0] || {
    totalRevenue: 0,
    averageRevenue: 0,
    courseCount: 0
  };

  // Get subscription revenue from users
  const subscriptionRevenue = await User.aggregate([
    {
      $match: {
        'subscription.plan': { $ne: 'free' },
        'subscription.status': 'active'
      }
    },
    {
      $group: {
        _id: '$subscription.plan',
        count: { $sum: 1 }
      }
    }
  ]);

  // Calculate estimated monthly subscription revenue
  const planPrices = { basic: 29, premium: 59, vip: 99 };
  const monthlySubscriptionRevenue = subscriptionRevenue.reduce((total, sub) => {
    return total + (sub.count * (planPrices[sub._id] || 0));
  }, 0);

  return {
    total: stats.totalRevenue,
    monthly: monthlySubscriptionRevenue,
    average: Math.round(stats.averageRevenue * 100) / 100,
    subscriptionBreakdown: subscriptionRevenue
  };
};

// Helper function to get activity statistics
const getActivityStats = async (startDate) => {
  const [lessonCompletions, testResults, studyStreaks] = await Promise.all([
    Lesson.aggregate([
      {
        $unwind: '$completions'
      },
      {
        $match: {
          'completions.completedAt': { $gte: startDate }
        }
      },
      {
        $group: {
          _id: null,
          totalCompletions: { $sum: 1 },
          averageTimeSpent: { $avg: '$completions.timeSpent' },
          averageScore: { $avg: '$completions.score' }
        }
      }
    ]),
    User.aggregate([
      {
        $unwind: '$testResults'
      },
      {
        $match: {
          'testResults.completedAt': { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$testResults.section',
          count: { $sum: 1 },
          averageScore: { $avg: '$testResults.score' }
        }
      }
    ]),
    User.aggregate([
      {
        $match: {
          'studyStreak.current': { $gt: 0 }
        }
      },
      {
        $group: {
          _id: null,
          averageStreak: { $avg: '$studyStreak.current' },
          maxStreak: { $max: '$studyStreak.longest' },
          activeStreaks: { $sum: 1 }
        }
      }
    ])
  ]);

  const lessonStats = lessonCompletions[0] || {
    totalCompletions: 0,
    averageTimeSpent: 0,
    averageScore: 0
  };

  const streakStats = studyStreaks[0] || {
    averageStreak: 0,
    maxStreak: 0,
    activeStreaks: 0
  };

  return {
    lessonCompletions: lessonStats.totalCompletions,
    averageTimeSpent: Math.round(lessonStats.averageTimeSpent || 0),
    averageScore: Math.round((lessonStats.averageScore || 0) * 100) / 100,
    testResults,
    studyStreaks: {
      average: Math.round((streakStats.averageStreak || 0) * 100) / 100,
      maximum: streakStats.maxStreak || 0,
      activeUsers: streakStats.activeStreaks || 0
    }
  };
};

// @desc    Get user analytics trends
// @route   GET /api/v1/analytics/users/trends
// @access  Private/Admin
export const getUserTrends = async (req, res) => {
  try {
    const { timeframe = '30d', interval = 'day' } = req.query;

    const now = new Date();
    let startDate;
    let groupFormat;

    switch (timeframe) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        groupFormat = { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } };
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        groupFormat = { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } };
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        groupFormat = { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } };
        break;
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        groupFormat = { $dateToString: { format: "%Y-%m", date: "$createdAt" } };
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        groupFormat = { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } };
    }

    const userTrends = await User.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate },
          isActive: true,
          isBlocked: false
        }
      },
      {
        $group: {
          _id: groupFormat,
          newUsers: { $sum: 1 },
          students: { $sum: { $cond: [{ $eq: ['$role', 'student'] }, 1, 0] } },
          instructors: { $sum: { $cond: [{ $eq: ['$role', 'instructor'] }, 1, 0] } }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    res.json({
      success: true,
      data: {
        timeframe,
        interval,
        trends: userTrends
      }
    });
  } catch (error) {
    console.error('User trends error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching user trends'
    });
  }
};

// @desc    Get course performance analytics
// @route   GET /api/v1/analytics/courses/performance
// @access  Private/Admin
export const getCoursePerformance = async (req, res) => {
  try {
    const { limit = 10, sortBy = 'enrollments' } = req.query;

    const sortOptions = {
      enrollments: { 'stats.totalEnrollments': -1 },
      rating: { 'stats.averageRating': -1 },
      revenue: { 'stats.totalRevenue': -1 },
      completion: { 'stats.completionRate': -1 }
    };

    const courses = await Course.find({
      status: 'published',
      isPublished: true
    })
    .select('title category level stats analytics instructor')
    .populate('instructor', 'firstName lastName')
    .sort(sortOptions[sortBy] || sortOptions.enrollments)
    .limit(Number(limit));

    // Calculate additional metrics
    const performanceData = courses.map(course => ({
      id: course._id,
      title: course.title,
      category: course.category,
      level: course.level,
      instructor: course.instructor,
      metrics: {
        enrollments: course.stats.totalEnrollments || 0,
        completionRate: course.stats.completionRate || 0,
        averageRating: course.stats.averageRating || 0,
        totalRatings: course.stats.totalRatings || 0,
        revenue: course.stats.totalRevenue || 0,
        views: course.analytics.views || 0,
        conversionRate: course.analytics.conversionRate || 0
      }
    }));

    res.json({
      success: true,
      data: {
        courses: performanceData,
        sortBy,
        limit: Number(limit)
      }
    });
  } catch (error) {
    console.error('Course performance error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching course performance'
    });
  }
};

// @desc    Get revenue analytics
// @route   GET /api/v1/analytics/revenue
// @access  Private/Admin
export const getRevenueAnalytics = async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;

    const now = new Date();
    let startDate;

    switch (timeframe) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // Get course revenue breakdown
    const courseRevenue = await Course.aggregate([
      {
        $match: {
          'stats.totalRevenue': { $gt: 0 }
        }
      },
      {
        $group: {
          _id: '$category',
          totalRevenue: { $sum: '$stats.totalRevenue' },
          courseCount: { $sum: 1 },
          averageRevenue: { $avg: '$stats.totalRevenue' }
        }
      },
      {
        $sort: { totalRevenue: -1 }
      }
    ]);

    // Get subscription revenue breakdown
    const subscriptionStats = await User.aggregate([
      {
        $match: {
          'subscription.plan': { $ne: 'free' },
          'subscription.status': 'active'
        }
      },
      {
        $group: {
          _id: '$subscription.plan',
          count: { $sum: 1 }
        }
      }
    ]);

    const planPrices = { basic: 29, premium: 59, vip: 99 };
    const subscriptionRevenue = subscriptionStats.map(stat => ({
      plan: stat._id,
      subscribers: stat.count,
      monthlyRevenue: stat.count * planPrices[stat._id],
      annualRevenue: stat.count * planPrices[stat._id] * 12
    }));

    const totalSubscriptionRevenue = subscriptionRevenue.reduce(
      (total, sub) => total + sub.monthlyRevenue, 0
    );

    const totalCourseRevenue = courseRevenue.reduce(
      (total, course) => total + course.totalRevenue, 0
    );

    res.json({
      success: true,
      data: {
        timeframe,
        summary: {
          totalRevenue: totalCourseRevenue + totalSubscriptionRevenue,
          courseRevenue: totalCourseRevenue,
          subscriptionRevenue: totalSubscriptionRevenue
        },
        breakdown: {
          courses: courseRevenue,
          subscriptions: subscriptionRevenue
        }
      }
    });
  } catch (error) {
    console.error('Revenue analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching revenue analytics'
    });
  }
};
