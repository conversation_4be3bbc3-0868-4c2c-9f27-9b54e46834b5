import User from '../models/User.js';
import jwt from 'jsonwebtoken';

// Helper function to generate tokens
const generateTokens = (user) => {
  const accessToken = user.generateAuthToken();
  const refreshToken = user.generateRefreshToken();
  return { accessToken, refreshToken };
};

// Helper function to set token cookies
const setTokenCookies = (res, accessToken, refreshToken) => {
  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
  };

  res.cookie('token', accessToken, cookieOptions);
  res.cookie('refreshToken', refreshToken, {
    ...cookieOptions,
    maxAge: 30 * 24 * 60 * 60 * 1000 // 30 days
  });
};

// @desc    Google OAuth login
// @route   POST /api/v1/auth/google
// @access  Public
export const googleAuth = async (req, res) => {
  try {
    const { credential } = req.body;

    if (!credential) {
      return res.status(400).json({
        success: false,
        message: 'Google credential is required'
      });
    }

    // Decode the Google JWT token (without verification for demo purposes)
    // In production, you should verify the token with Google's public keys
    const decoded = jwt.decode(credential);
    
    if (!decoded || !decoded.email) {
      return res.status(400).json({
        success: false,
        message: 'Invalid Google credential'
      });
    }

    const { email, given_name, family_name, picture, sub } = decoded;

    // Check if user already exists
    let user = await User.findOne({ 
      $or: [
        { email: email.toLowerCase() },
        { ssoId: sub, ssoProvider: 'google' }
      ]
    });

    if (user) {
      // Update existing user with Google info if not already set
      if (!user.ssoProvider) {
        user.ssoProvider = 'google';
        user.ssoId = sub;
        user.isEmailVerified = true;
        if (picture && !user.avatar?.url) {
          user.avatar = { url: picture };
        }
        await user.save();
      }
    } else {
      // Create new user
      user = await User.create({
        firstName: given_name || 'Google',
        lastName: family_name || 'User',
        email: email.toLowerCase(),
        ssoProvider: 'google',
        ssoId: sub,
        isEmailVerified: true,
        isActive: true,
        avatar: picture ? { url: picture } : undefined,
        role: 'student'
      });
    }

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user);

    // Set cookies
    setTokenCookies(res, accessToken, refreshToken);

    // Update login stats
    user.loginCount += 1;
    user.lastLoginAt = new Date();
    await user.save();

    res.json({
      success: true,
      message: 'Google login successful',
      data: {
        user: {
          id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          role: user.role,
          avatar: user.avatar,
          isEmailVerified: user.isEmailVerified,
          subscription: user.subscription,
          ssoProvider: user.ssoProvider
        },
        accessToken,
        refreshToken
      }
    });
  } catch (error) {
    console.error('Google auth error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during Google authentication'
    });
  }
};

// @desc    Get Google OAuth URL (for traditional OAuth flow)
// @route   GET /api/v1/auth/google/url
// @access  Public
export const getGoogleAuthUrl = async (req, res) => {
  try {
    const clientId = process.env.GOOGLE_CLIENT_ID;
    const redirectUri = `${process.env.FRONTEND_URL}/auth/google/callback`;
    
    const scope = 'openid email profile';
    const responseType = 'code';
    const state = jwt.sign({ timestamp: Date.now() }, process.env.JWT_SECRET, { expiresIn: '10m' });

    const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
      `client_id=${clientId}&` +
      `redirect_uri=${encodeURIComponent(redirectUri)}&` +
      `scope=${encodeURIComponent(scope)}&` +
      `response_type=${responseType}&` +
      `state=${state}`;

    res.json({
      success: true,
      data: {
        authUrl: googleAuthUrl,
        state
      }
    });
  } catch (error) {
    console.error('Google auth URL error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error generating Google auth URL'
    });
  }
};

// @desc    Handle Google OAuth callback
// @route   POST /api/v1/auth/google/callback
// @access  Public
export const googleCallback = async (req, res) => {
  try {
    const { code, state } = req.body;

    if (!code || !state) {
      return res.status(400).json({
        success: false,
        message: 'Authorization code and state are required'
      });
    }

    // Verify state
    try {
      jwt.verify(state, process.env.JWT_SECRET);
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: 'Invalid state parameter'
      });
    }

    // Exchange code for tokens
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: process.env.GOOGLE_CLIENT_ID,
        client_secret: process.env.GOOGLE_CLIENT_SECRET,
        code,
        grant_type: 'authorization_code',
        redirect_uri: `${process.env.FRONTEND_URL}/auth/google/callback`,
      }),
    });

    const tokenData = await tokenResponse.json();

    if (!tokenData.access_token) {
      return res.status(400).json({
        success: false,
        message: 'Failed to get access token from Google'
      });
    }

    // Get user info from Google
    const userResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        Authorization: `Bearer ${tokenData.access_token}`,
      },
    });

    const googleUser = await userResponse.json();

    if (!googleUser.email) {
      return res.status(400).json({
        success: false,
        message: 'Failed to get user info from Google'
      });
    }

    // Check if user already exists
    let user = await User.findOne({ 
      $or: [
        { email: googleUser.email.toLowerCase() },
        { ssoId: googleUser.id, ssoProvider: 'google' }
      ]
    });

    if (user) {
      // Update existing user
      if (!user.ssoProvider) {
        user.ssoProvider = 'google';
        user.ssoId = googleUser.id;
        user.isEmailVerified = true;
        if (googleUser.picture && !user.avatar?.url) {
          user.avatar = { url: googleUser.picture };
        }
        await user.save();
      }
    } else {
      // Create new user
      user = await User.create({
        firstName: googleUser.given_name || 'Google',
        lastName: googleUser.family_name || 'User',
        email: googleUser.email.toLowerCase(),
        ssoProvider: 'google',
        ssoId: googleUser.id,
        isEmailVerified: true,
        isActive: true,
        avatar: googleUser.picture ? { url: googleUser.picture } : undefined,
        role: 'student'
      });
    }

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user);

    // Set cookies
    setTokenCookies(res, accessToken, refreshToken);

    // Update login stats
    user.loginCount += 1;
    user.lastLoginAt = new Date();
    await user.save();

    res.json({
      success: true,
      message: 'Google login successful',
      data: {
        user: {
          id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          role: user.role,
          avatar: user.avatar,
          isEmailVerified: user.isEmailVerified,
          subscription: user.subscription,
          ssoProvider: user.ssoProvider
        },
        accessToken,
        refreshToken
      }
    });
  } catch (error) {
    console.error('Google callback error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during Google authentication'
    });
  }
};
