import express from 'express';
import {
  getDashboardAnalytics,
  getUserTrends,
  getCoursePerformance,
  getRevenueAnalytics
} from '../controllers/analyticsController.js';
import { protect, authorize } from '../middleware/auth.js';

const router = express.Router();

// All analytics routes require authentication and admin role
router.use(protect);
router.use(authorize('admin'));

// Analytics routes
router.get('/dashboard', getDashboardAnalytics);
router.get('/users/trends', getUserTrends);
router.get('/courses/performance', getCoursePerformance);
router.get('/revenue', getRevenueAnalytics);

export default router;
