import express from 'express';
import {
  getUsers,
  getUser,
  updateUser,
  toggleUserBlock,
  deleteUser,
  getUserActivity
} from '../controllers/userController.js';
import {
  validateUserQuery,
  validateUpdateUser,
  validateBlockUser,
  validateObjectId
} from '../middleware/validation.js';
import { protect, authorize } from '../middleware/auth.js';

const router = express.Router();

// All routes require authentication and admin role
router.use(protect);
router.use(authorize('admin'));

// User management routes
router.get('/', validateUserQuery, getUsers);
router.get('/:id', validateObjectId(), getUser);
router.put('/:id', validateObjectId(), validateUpdateUser, updateUser);
router.put('/:id/block', validateObjectId(), validateBlockUser, toggleUserBlock);
router.delete('/:id', validateObjectId(), deleteUser);
router.get('/:id/activity', validateObjectId(), getUserActivity);

export default router;
