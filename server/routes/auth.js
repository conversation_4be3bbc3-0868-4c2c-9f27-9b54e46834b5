import express from 'express';
import {
  register,
  login,
  logout,
  getMe,
  refreshToken,
  forgotPassword,
  resetPassword,
  changePassword
} from '../controllers/authController.js';
import {
  googleAuth,
  getGoogleAuthUrl,
  googleCallback
} from '../controllers/oauthController.js';
import {
  validateRegister,
  validateLogin,
  validateChangePassword,
  validateForgotPassword,
  validateResetPassword
} from '../middleware/validation.js';
import { protect, sensitiveOperation } from '../middleware/auth.js';

const router = express.Router();

// Public routes
router.post('/register', validateRegister, register);
router.post('/login', validateLogin, login);
router.post('/refresh', refreshToken);
router.post('/forgot-password', validateForgotPassword, forgotPassword);
router.put('/reset-password/:resetToken', validateResetPassword, resetPassword);

// OAuth routes
router.post('/google', googleAuth);
router.get('/google/url', getGoogleAuthUrl);
router.post('/google/callback', googleCallback);

// Protected routes
router.use(protect); // All routes below require authentication

router.get('/me', getMe);
router.post('/logout', logout);
router.put('/change-password', validateChangePassword, sensitiveOperation, changePassword);

export default router;
