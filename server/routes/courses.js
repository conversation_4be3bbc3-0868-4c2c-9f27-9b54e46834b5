import express from 'express';
import {
  getCourses,
  getCourse,
  createCourse,
  updateCourse,
  deleteCourse,
  enrollInCourse,
  getMyEnrolledCourses,
  addCourseReview
} from '../controllers/courseController.js';
import {
  validateCreateCourse,
  validateUpdateCourse,
  validateAddReview,
  validateCourseQuery,
  validateObjectId
} from '../middleware/validation.js';
import {
  protect,
  authorize,
  optionalAuth,
  checkCourseEnrollment
} from '../middleware/auth.js';

const router = express.Router();

// Public routes (with optional authentication)
router.get('/', optionalAuth, validateCourseQuery, getCourses);
router.get('/:id', optionalAuth, validateObjectId(), getCourse);

// Protected routes
router.use(protect); // All routes below require authentication

// Student routes
router.get('/my/enrolled', getMyEnrolledCourses);
router.post('/:id/enroll', validateObjectId(), enrollInCourse);
router.post('/:id/reviews', validateObjectId(), validateAddReview, addCourseReview);

// Instructor/Admin routes
router.post('/', authorize('instructor', 'admin'), validateCreateCourse, createCourse);
router.put('/:id', authorize('instructor', 'admin'), validateUpdateCourse, updateCourse);
router.delete('/:id', authorize('instructor', 'admin'), validateObjectId(), deleteCourse);

export default router;
