import { useState, useEffect, useCallback } from 'react';
import { analyticsAPI } from '../utils/api';

export const useAnalytics = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [userTrends, setUserTrends] = useState(null);
  const [coursePerformance, setCoursePerformance] = useState(null);
  const [revenueData, setRevenueData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchDashboardAnalytics = useCallback(async (timeframe = '30d') => {
    try {
      setLoading(true);
      setError(null);
      const response = await analyticsAPI.getDashboardAnalytics({ timeframe });
      setDashboardData(response.data);
      return response.data;
    } catch (err) {
      console.error('Error fetching dashboard analytics:', err);
      setError('Failed to load dashboard analytics');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchUserTrends = useCallback(async (params = {}) => {
    try {
      setLoading(true);
      setError(null);
      const response = await analyticsAPI.getUserTrends(params);
      setUserTrends(response.data);
      return response.data;
    } catch (err) {
      console.error('Error fetching user trends:', err);
      setError('Failed to load user trends');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchCoursePerformance = useCallback(async (params = {}) => {
    try {
      setLoading(true);
      setError(null);
      const response = await analyticsAPI.getCoursePerformance(params);
      setCoursePerformance(response.data);
      return response.data;
    } catch (err) {
      console.error('Error fetching course performance:', err);
      setError('Failed to load course performance');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchRevenueAnalytics = useCallback(async (params = {}) => {
    try {
      setLoading(true);
      setError(null);
      const response = await analyticsAPI.getRevenueAnalytics(params);
      setRevenueData(response.data);
      return response.data;
    } catch (err) {
      console.error('Error fetching revenue analytics:', err);
      setError('Failed to load revenue analytics');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshAll = useCallback(async (timeframe = '30d') => {
    try {
      setLoading(true);
      setError(null);
      
      const [dashboard, trends, performance, revenue] = await Promise.allSettled([
        analyticsAPI.getDashboardAnalytics({ timeframe }),
        analyticsAPI.getUserTrends({ timeframe }),
        analyticsAPI.getCoursePerformance({ limit: 10 }),
        analyticsAPI.getRevenueAnalytics({ timeframe })
      ]);

      if (dashboard.status === 'fulfilled') {
        setDashboardData(dashboard.value.data);
      }
      if (trends.status === 'fulfilled') {
        setUserTrends(trends.value.data);
      }
      if (performance.status === 'fulfilled') {
        setCoursePerformance(performance.value.data);
      }
      if (revenue.status === 'fulfilled') {
        setRevenueData(revenue.value.data);
      }

      // Check if any requests failed
      const failures = [dashboard, trends, performance, revenue].filter(
        result => result.status === 'rejected'
      );
      
      if (failures.length > 0) {
        setError(`Failed to load ${failures.length} analytics sections`);
      }
    } catch (err) {
      console.error('Error refreshing analytics:', err);
      setError('Failed to refresh analytics data');
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    // Data
    dashboardData,
    userTrends,
    coursePerformance,
    revenueData,
    
    // State
    loading,
    error,
    
    // Actions
    fetchDashboardAnalytics,
    fetchUserTrends,
    fetchCoursePerformance,
    fetchRevenueAnalytics,
    refreshAll,
    
    // Utilities
    clearError: () => setError(null)
  };
};
