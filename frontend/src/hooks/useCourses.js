import { useState, useEffect, useCallback } from 'react';
import { courseAPI } from '../utils/api';

export const useCourses = (initialParams = {}) => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  // Fetch courses with parameters
  const fetchCourses = useCallback(async (params = {}) => {
    setLoading(true);
    setError(null);

    try {
      const response = await courseAPI.getCourses({
        ...initialParams,
        ...params
      });

      if (response.success) {
        // Handle both paginated and non-paginated responses
        const coursesData = response.data.courses || response.data;
        const paginationData = response.data.pagination;

        setCourses(Array.isArray(coursesData) ? coursesData : []);
        setPagination(paginationData || {
          page: 1,
          limit: 10,
          total: Array.isArray(coursesData) ? coursesData.length : 0,
          totalPages: 1
        });
      } else {
        throw new Error(response.message || 'Failed to fetch courses');
      }
    } catch (err) {
      setError(err.message || 'Failed to fetch courses');
      setCourses([]);
    } finally {
      setLoading(false);
    }
  }, [initialParams]);

  // Create a new course
  const createCourse = useCallback(async (courseData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await courseAPI.createCourse(courseData);

      if (response.success) {
        // The course data is nested in response.data.course
        const newCourse = response.data.course || response.data;
        // Add the new course to local state instead of refetching
        setCourses(prevCourses => [newCourse, ...prevCourses]);
        return newCourse;
      } else {
        throw new Error(response.message || 'Failed to create course');
      }
    } catch (err) {
      setError(err.message || 'Failed to create course');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Update a course
  const updateCourse = useCallback(async (courseId, courseData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await courseAPI.updateCourse(courseId, courseData);

      if (response.success) {
        // The course data is nested in response.data.course
        const updatedCourse = response.data.course || response.data;
        // Update the course in the local state
        setCourses(prevCourses =>
          prevCourses.map(course =>
            course._id === courseId ? { ...course, ...updatedCourse } : course
          )
        );
        return updatedCourse;
      } else {
        throw new Error(response.message || 'Failed to update course');
      }
    } catch (err) {
      setError(err.message || 'Failed to update course');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Delete a course
  const deleteCourse = useCallback(async (courseId) => {
    setLoading(true);
    setError(null);

    try {
      const response = await courseAPI.deleteCourse(courseId);

      if (response.success) {
        // Remove the course from local state
        setCourses(prevCourses =>
          prevCourses.filter(course => course._id !== courseId)
        );
        return true;
      } else {
        throw new Error(response.message || 'Failed to delete course');
      }
    } catch (err) {
      setError(err.message || 'Failed to delete course');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Toggle course status (publish/unpublish)
  const toggleCourseStatus = useCallback(async (courseId, status) => {
    setLoading(true);
    setError(null);

    try {
      const response = await courseAPI.toggleCourseStatus(courseId, status);

      if (response.success) {
        // Update the course status in local state
        setCourses(prevCourses =>
          prevCourses.map(course =>
            course._id === courseId ? { ...course, status, isPublished: status === 'published' } : course
          )
        );
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to update course status');
      }
    } catch (err) {
      setError(err.message || 'Failed to update course status');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Bulk operations
  const bulkUpdateCourses = useCallback(async (courseIds, updates) => {
    setLoading(true);
    setError(null);

    try {
      const response = await courseAPI.bulkUpdateCourses(courseIds, updates);

      if (response.success) {
        // Update courses in local state instead of refetching
        setCourses(prevCourses =>
          prevCourses.map(course =>
            courseIds.includes(course._id) ? { ...course, ...updates } : course
          )
        );
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to update courses');
      }
    } catch (err) {
      setError(err.message || 'Failed to update courses');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const bulkDeleteCourses = useCallback(async (courseIds) => {
    setLoading(true);
    setError(null);

    try {
      const response = await courseAPI.bulkDeleteCourses(courseIds);

      if (response.success) {
        // Remove the courses from local state
        setCourses(prevCourses =>
          prevCourses.filter(course => !courseIds.includes(course._id))
        );
        return true;
      } else {
        throw new Error(response.message || 'Failed to delete courses');
      }
    } catch (err) {
      setError(err.message || 'Failed to delete courses');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial fetch on mount
  useEffect(() => {
    fetchCourses();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return {
    courses,
    loading,
    error,
    pagination,
    fetchCourses,
    createCourse,
    updateCourse,
    deleteCourse,
    toggleCourseStatus,
    bulkUpdateCourses,
    bulkDeleteCourses,
    refetch: fetchCourses
  };
};

export default useCourses;
