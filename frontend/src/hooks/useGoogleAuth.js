import { useCallback, useEffect, useState } from 'react';
import { useAuth } from '../context/AuthContext';

const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID || 'demo-client-id';

export const useGoogleAuth = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const { login } = useAuth();

  // Initialize Google Identity Services
  useEffect(() => {
    const initializeGoogleAuth = () => {
      if (window.google && window.google.accounts) {
        window.google.accounts.id.initialize({
          client_id: GOOGLE_CLIENT_ID,
          callback: handleCredentialResponse,
          auto_select: false,
          cancel_on_tap_outside: true,
        });
      }
    };

    // Check if Google script is loaded
    if (window.google) {
      initializeGoogleAuth();
    } else {
      // Wait for Google script to load
      const checkGoogleLoaded = setInterval(() => {
        if (window.google) {
          initializeGoogleAuth();
          clearInterval(checkGoogleLoaded);
        }
      }, 100);

      // Cleanup interval after 10 seconds
      setTimeout(() => clearInterval(checkGoogleLoaded), 10000);
    }
  }, []);

  const handleCredentialResponse = async (response) => {
    setIsLoading(true);
    setError(null);

    try {
      // Send the credential to our backend
      const result = await fetch('http://localhost:5000/api/v1/auth/google', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          credential: response.credential,
        }),
      });

      const data = await result.json();

      if (data.success) {
        // Update auth context with user data
        await login(data.data.user.email, null, data.data);
        return { success: true, user: data.data.user };
      } else {
        throw new Error(data.message || 'Google authentication failed');
      }
    } catch (err) {
      console.error('Google auth error:', err);
      setError(err.message || 'Google authentication failed');
      return { success: false, error: err.message };
    } finally {
      setIsLoading(false);
    }
  };

  const signInWithGoogle = useCallback(() => {
    setError(null);
    
    if (!window.google || !window.google.accounts) {
      setError('Google authentication is not available');
      return;
    }

    try {
      window.google.accounts.id.prompt((notification) => {
        if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
          console.log('Google One Tap not displayed or skipped');
          // Fallback to popup
          window.google.accounts.id.renderButton(
            document.getElementById('google-signin-button'),
            {
              theme: 'outline',
              size: 'large',
              width: '100%',
            }
          );
        }
      });
    } catch (err) {
      console.error('Error showing Google prompt:', err);
      setError('Failed to show Google sign-in');
    }
  }, []);

  const renderGoogleButton = useCallback((elementId, options = {}) => {
    if (!window.google || !window.google.accounts) {
      console.warn('Google authentication not available');
      return;
    }

    const defaultOptions = {
      theme: 'outline',
      size: 'large',
      width: '100%',
      text: 'signin_with',
      shape: 'rectangular',
      logo_alignment: 'left',
      ...options,
    };

    try {
      window.google.accounts.id.renderButton(
        document.getElementById(elementId),
        defaultOptions
      );
    } catch (err) {
      console.error('Error rendering Google button:', err);
      setError('Failed to render Google sign-in button');
    }
  }, []);

  return {
    signInWithGoogle,
    renderGoogleButton,
    isLoading,
    error,
    isGoogleLoaded: !!(window.google && window.google.accounts),
  };
};
