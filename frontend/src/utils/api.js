// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api/v1';

// API utility class for making authenticated requests
class ApiClient {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Get authentication headers
  getAuthHeaders() {
    const token = localStorage.getItem('pte_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  }

  // Generic request method
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: this.getAuthHeaders(),
      ...options,
      headers: {
        ...this.getAuthHeaders(),
        ...options.headers
      }
    };

    console.log(`API Request: ${options.method || 'GET'} ${url}`);
    console.log('Headers:', config.headers);

    try {
      const response = await fetch(url, config);

      console.log(`API Response: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        if (response.status === 401) {
          // Token might be expired, redirect to login
          localStorage.removeItem('pte_token');
          localStorage.removeItem('pte_user');
          window.location.href = '/login';
          throw new Error('Authentication failed. Please login again.');
        }

        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('API Data:', data);
      return data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // GET request
  async get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;
    return this.request(url, { method: 'GET' });
  }

  // POST request
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  // PUT request
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  // DELETE request
  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' });
  }
}

// Create and export a singleton instance
const apiClient = new ApiClient();

// User Management API methods
export const userAPI = {
  // Get all users with filters and pagination
  getUsers: (params = {}) => apiClient.get('/users', params),

  // Get single user by ID
  getUser: (id) => apiClient.get(`/users/${id}`),

  // Update user
  updateUser: (id, data) => apiClient.put(`/users/${id}`, data),

  // Block/unblock user
  toggleUserBlock: (id, reason = '') => apiClient.put(`/users/${id}/block`, { reason }),

  // Delete user
  deleteUser: (id) => apiClient.delete(`/users/${id}`),

  // Get user activity
  getUserActivity: (id) => apiClient.get(`/users/${id}/activity`)
};

// Auth API methods
export const authAPI = {
  login: (email, password) => apiClient.post('/auth/login', { email, password }),
  register: (userData) => apiClient.post('/auth/register', userData),
  logout: () => apiClient.post('/auth/logout'),
  getMe: () => apiClient.get('/auth/me'),
  refreshToken: () => apiClient.post('/auth/refresh')
};

// Course API methods
export const courseAPI = {
  // Get all courses with filters and pagination
  getCourses: (params = {}) => apiClient.get('/courses', params),

  // Get single course by ID
  getCourse: (id) => apiClient.get(`/courses/${id}`),

  // Create new course
  createCourse: (data) => apiClient.post('/courses', data),

  // Update course
  updateCourse: (id, data) => apiClient.put(`/courses/${id}`, data),

  // Delete course
  deleteCourse: (id) => apiClient.delete(`/courses/${id}`),

  // Publish/unpublish course
  toggleCourseStatus: (id, status) => apiClient.put(`/courses/${id}/status`, { status }),

  // Get course statistics
  getCourseStats: (id) => apiClient.get(`/courses/${id}/stats`),

  // Get enrolled students for a course
  getCourseEnrollments: (id, params = {}) => apiClient.get(`/courses/${id}/enrollments`, params),

  // Get course reviews
  getCourseReviews: (id, params = {}) => apiClient.get(`/courses/${id}/reviews`, params),

  // Bulk operations
  bulkUpdateCourses: (courseIds, updates) => apiClient.put('/courses/bulk', { courseIds, updates }),
  bulkDeleteCourses: (courseIds) => apiClient.delete('/courses/bulk', { data: { courseIds } }),

  // Course categories and levels
  getCategories: () => apiClient.get('/courses/categories'),
  getLevels: () => apiClient.get('/courses/levels')
};

// Analytics API methods
export const analyticsAPI = {
  // Get dashboard analytics
  getDashboardAnalytics: (params = {}) => apiClient.get('/analytics/dashboard', params),

  // Get user trends
  getUserTrends: (params = {}) => apiClient.get('/analytics/users/trends', params),

  // Get course performance
  getCoursePerformance: (params = {}) => apiClient.get('/analytics/courses/performance', params),

  // Get revenue analytics
  getRevenueAnalytics: (params = {}) => apiClient.get('/analytics/revenue', params)
};

export default apiClient;
