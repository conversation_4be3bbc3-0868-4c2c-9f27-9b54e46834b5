import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

// Layout Components
import DashboardLayout from './components/layout/DashboardLayout';

// Auth Components
import Login from './components/auth/Login';
import Register from './components/auth/Register';

// Dashboard Components
import StudentDashboard from './components/dashboard/StudentDashboard';
import AdminDashboard from './components/dashboard/AdminDashboard';

// Admin Components
import UserManagement from './components/admin/UserManagement';
import CourseManagement from './components/admin/CourseManagement';
import ContentManagement from './components/admin/ContentManagement';

// Analytics Components
import AnalyticsPage from './components/analytics/AnalyticsPage';

// Course Components
// import CourseCatalog from './components/courses/CourseCatalog';

// Context for user authentication
import { AuthProvider, useAuth } from './context/AuthContext';

// Dashboard Router Component
const DashboardRouter = () => {
  const { user } = useAuth();

  if (user?.role === 'admin') {
    return (
      <DashboardLayout>
        <AdminDashboard />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <StudentDashboard />
    </DashboardLayout>
  );
};

// Protected Route Component
const ProtectedRoute = ({ children, requiredRole = null }) => {
  let { user, isAuthenticated } = useAuth();

  // isAuthenticated = true;


  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (requiredRole && user?.role !== requiredRole) {
    return <Navigate to="/dashboard" replace />;
  }

  return children;
};

// Public Route Component (redirect if already authenticated)
const PublicRoute = ({ children }) => {
  const { isAuthenticated } = useAuth();

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  return children;
};

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Routes>
            {/* Public Routes */}
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route
              path="/login"
              element={
                <PublicRoute>
                  <Login />
                </PublicRoute>
              }
            />
            <Route
              path="/register"
              element={
                <PublicRoute>
                  <Register />
                </PublicRoute>
              }
            />

            {/* Protected Routes */}
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <DashboardRouter />
                </ProtectedRoute>
              }
            />

            <Route
              path="/courses"
              element={
                <ProtectedRoute>
                  <DashboardLayout>
                    <div>Hello World</div>
                    {/* <CourseCatalog /> */}
                  </DashboardLayout>
                </ProtectedRoute>
              }
            />

            {/* Admin Routes */}
            <Route
              path="/admin/users"
              element={
                <ProtectedRoute requiredRole="admin">
                  <DashboardLayout>
                    <UserManagement />
                  </DashboardLayout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/courses"
              element={
                <ProtectedRoute requiredRole="admin">
                  <DashboardLayout>
                    <CourseManagement />
                  </DashboardLayout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/analytics"
              element={
                <ProtectedRoute requiredRole="admin">
                  <DashboardLayout>
                    <AnalyticsPage />
                  </DashboardLayout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/content"
              element={
                <ProtectedRoute requiredRole="admin">
                  <DashboardLayout>
                    <ContentManagement />
                  </DashboardLayout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/*"
              element={
                <ProtectedRoute requiredRole="admin">
                  <DashboardLayout>
                    {/* Other admin components will go here */}
                    <div className="p-8 text-center">
                      <h1 className="text-2xl font-bold">Admin Feature</h1>
                      <p className="text-gray-600 mt-2">This admin feature is coming soon...</p>
                    </div>
                  </DashboardLayout>
                </ProtectedRoute>
              }
            />

            {/* Catch all route */}
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
