import Sidebar from './Sidebar';
import { useAuth } from '../../context/AuthContext';

const DashboardLayout = ({ userRole, children }) => {
  const { user } = useAuth();

  // Use the actual user role from AuthContext, fallback to prop, then default to 'student'
  const actualUserRole = user?.role || userRole || 'student';

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <Sidebar userRole={actualUserRole} />

      <div className="flex-1 lg:ml-0">
        <main className="p-4 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;
