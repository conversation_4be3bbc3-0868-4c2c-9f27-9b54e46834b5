import { useState, useEffect } from 'react';
import {
  Plus,
  Search,
  Calendar,
  Clock,
  Users,
  Video,
  Edit,
  Trash2,
  Eye,
  CheckCircle,
  XCircle,
  AlertCircle,
  Play,
  UserCheck,
  Globe,
  Link
} from 'lucide-react';
import { courseAPI } from '../../utils/api';

const LiveSessionManagement = () => {
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [showSessionForm, setShowSessionForm] = useState(false);
  const [editingSession, setEditingSession] = useState(null);
  const [selectedTimeframe, setSelectedTimeframe] = useState('upcoming');

  const sessionStatuses = [
    { id: 'all', name: 'All Sessions', color: 'gray' },
    { id: 'scheduled', name: 'Scheduled', color: 'blue' },
    { id: 'live', name: 'Live Now', color: 'green' },
    { id: 'completed', name: 'Completed', color: 'gray' },
    { id: 'cancelled', name: 'Cancelled', color: 'red' }
  ];

  const timeframes = [
    { id: 'upcoming', name: 'Upcoming Sessions' },
    { id: 'today', name: 'Today' },
    { id: 'week', name: 'This Week' },
    { id: 'month', name: 'This Month' },
    { id: 'all', name: 'All Time' }
  ];

  useEffect(() => {
    fetchSessions();
  }, [selectedTimeframe]);

  const fetchSessions = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement live sessions API endpoint
      // For now, using mock data
      const mockSessions = [
        {
          _id: '1',
          title: 'PTE Speaking Masterclass',
          description: 'Advanced speaking techniques and practice',
          instructor: {
            firstName: 'Dr. Sarah',
            lastName: 'Johnson',
            avatar: '/api/placeholder/40/40'
          },
          scheduledDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
          duration: 90, // 90 minutes
          maxParticipants: 25,
          registeredCount: 18,
          status: 'scheduled',
          meetingLink: 'https://zoom.us/j/123456789',
          category: 'speaking',
          level: 'intermediate',
          price: 49,
          isRecorded: true
        },
        {
          _id: '2',
          title: 'Writing Task 1 & 2 Workshop',
          description: 'Comprehensive writing strategies and feedback',
          instructor: {
            firstName: 'Prof. Michael',
            lastName: 'Chen',
            avatar: '/api/placeholder/40/40'
          },
          scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
          duration: 120, // 2 hours
          maxParticipants: 30,
          registeredCount: 22,
          status: 'scheduled',
          meetingLink: 'https://zoom.us/j/987654321',
          category: 'writing',
          level: 'all',
          price: 59,
          isRecorded: true
        },
        {
          _id: '3',
          title: 'Mock Test & Score Analysis',
          description: 'Full PTE mock test with detailed feedback',
          instructor: {
            firstName: 'Dr. Emily',
            lastName: 'Watson',
            avatar: '/api/placeholder/40/40'
          },
          scheduledDate: new Date(Date.now() + 60 * 60 * 1000), // 1 hour from now
          duration: 180, // 3 hours
          maxParticipants: 20,
          registeredCount: 15,
          status: 'scheduled',
          meetingLink: 'https://zoom.us/j/456789123',
          category: 'mock-test',
          level: 'all',
          price: 79,
          isRecorded: false
        },
        {
          _id: '4',
          title: 'Reading Strategies Live Session',
          description: 'Interactive reading comprehension techniques',
          instructor: {
            firstName: 'James',
            lastName: 'Rodriguez',
            avatar: '/api/placeholder/40/40'
          },
          scheduledDate: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
          duration: 75,
          maxParticipants: 35,
          registeredCount: 28,
          status: 'completed',
          meetingLink: 'https://zoom.us/j/789123456',
          category: 'reading',
          level: 'beginner',
          price: 39,
          isRecorded: true
        }
      ];

      setSessions(mockSessions);
    } catch (err) {
      console.error('Error fetching sessions:', err);
      setError('Failed to load live sessions');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      scheduled: 'bg-blue-100 text-blue-800 border-blue-200',
      live: 'bg-green-100 text-green-800 border-green-200',
      completed: 'bg-gray-100 text-gray-800 border-gray-200',
      cancelled: 'bg-red-100 text-red-800 border-red-200'
    };
    return colors[status] || colors.scheduled;
  };

  const getStatusIcon = (status) => {
    const icons = {
      scheduled: Clock,
      live: Play,
      completed: CheckCircle,
      cancelled: XCircle
    };
    const Icon = icons[status] || Clock;
    return <Icon className="w-4 h-4" />;
  };

  const formatDate = (date) => {
    return new Intl.DateTimeFormat('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  };

  const formatDuration = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const isSessionLive = (session) => {
    const now = new Date();
    const sessionStart = new Date(session.scheduledDate);
    const sessionEnd = new Date(sessionStart.getTime() + session.duration * 60 * 1000);
    return now >= sessionStart && now <= sessionEnd;
  };

  const isSessionUpcoming = (session) => {
    const now = new Date();
    const sessionStart = new Date(session.scheduledDate);
    return sessionStart > now;
  };

  // Filter sessions based on search, status, and timeframe
  const filteredSessions = sessions.filter(session => {
    const matchesSearch = session.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         session.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || session.status === selectedStatus;

    // Update session status based on current time
    if (isSessionLive(session)) {
      session.status = 'live';
    } else if (isSessionUpcoming(session)) {
      session.status = 'scheduled';
    } else if (session.status !== 'cancelled') {
      session.status = 'completed';
    }

    return matchesSearch && matchesStatus;
  });

  const handleCreateSession = () => {
    setEditingSession(null);
    setShowSessionForm(true);
  };

  const handleEditSession = (session) => {
    setEditingSession(session);
    setShowSessionForm(true);
  };

  const handleDeleteSession = async (sessionId) => {
    if (window.confirm('Are you sure you want to cancel this session? Registered users will be notified.')) {
      // TODO: Implement cancel session API
      setSessions(sessions.map(session =>
        session._id === sessionId
          ? { ...session, status: 'cancelled' }
          : session
      ));
    }
  };

  const handleJoinSession = (session) => {
    if (session.meetingLink) {
      window.open(session.meetingLink, '_blank');
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="lg:col-span-3 h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
          <h3 className="text-xl font-semibold text-red-900 mb-2">Error Loading Content</h3>
          <p className="text-red-800 mb-4">{error}</p>
          <button 
            onClick={fetchCourses}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Live Session Management</h1>
          <p className="text-gray-600 mt-1">
            Schedule and manage live PTE training sessions for registered users
          </p>
        </div>
        <button
          onClick={handleCreateSession}
          className="bg-[#00d4aa] hover:bg-[#00d4aa]/90 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
        >
          <Plus className="w-4 h-4" />
          Schedule Session
        </button>
      </div>

      {/* Session Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {sessionStatuses.slice(1).map((status) => {
          const count = sessions.filter(session => {
            if (status.id === 'live') return isSessionLive(session);
            if (status.id === 'scheduled') return isSessionUpcoming(session);
            return session.status === status.id;
          }).length;

          return (
            <div key={status.id} className="bg-white rounded-xl p-6 shadow-sm border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{status.name}</p>
                  <p className="text-2xl font-bold text-gray-900">{count}</p>
                </div>
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                  status.id === 'live' ? 'bg-green-100' :
                  status.id === 'scheduled' ? 'bg-blue-100' :
                  status.id === 'completed' ? 'bg-gray-100' :
                  'bg-red-100'
                }`}>
                  {getStatusIcon(status.id)}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl p-6 shadow-sm border">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search sessions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#00d4aa] focus:border-transparent"
              />
            </div>
          </div>
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-[#00d4aa] focus:border-transparent"
          >
            {sessionStatuses.map(status => (
              <option key={status.id} value={status.id}>
                {status.name}
              </option>
            ))}
          </select>
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-[#00d4aa] focus:border-transparent"
          >
            {timeframes.map(timeframe => (
              <option key={timeframe.id} value={timeframe.id}>
                {timeframe.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Sessions List */}
      <div className="bg-white rounded-xl shadow-sm border">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Live Sessions</h3>
          <p className="text-gray-600 text-sm mt-1">
            {filteredSessions.length} session{filteredSessions.length !== 1 ? 's' : ''} found
          </p>
        </div>

        <div className="divide-y divide-gray-200">
          {filteredSessions.length === 0 ? (
            <div className="p-12 text-center">
              <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No Sessions Found</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || selectedStatus !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'Start by scheduling your first live session'
                }
              </p>
              {!searchTerm && selectedStatus === 'all' && (
                <button
                  onClick={handleCreateSession}
                  className="bg-[#00d4aa] hover:bg-[#00d4aa]/90 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Schedule First Session
                </button>
              )}
            </div>
          ) : (
            filteredSessions.map((session) => (
              <div key={session._id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="text-lg font-semibold text-gray-900">{session.title}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs border ${getStatusColor(session.status)}`}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(session.status)}
                          {session.status}
                        </div>
                      </span>
                      {isSessionLive(session) && (
                        <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium animate-pulse">
                          LIVE NOW
                        </span>
                      )}
                    </div>

                    <p className="text-gray-600 mb-3">{session.description}</p>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center gap-2 text-gray-600">
                        <Calendar className="w-4 h-4" />
                        {formatDate(session.scheduledDate)}
                      </div>
                      <div className="flex items-center gap-2 text-gray-600">
                        <Clock className="w-4 h-4" />
                        {formatDuration(session.duration)}
                      </div>
                      <div className="flex items-center gap-2 text-gray-600">
                        <Users className="w-4 h-4" />
                        {session.registeredCount}/{session.maxParticipants} registered
                      </div>
                      <div className="flex items-center gap-2 text-gray-600">
                        <UserCheck className="w-4 h-4" />
                        {session.instructor.firstName} {session.instructor.lastName}
                      </div>
                    </div>

                    {session.meetingLink && (
                      <div className="mt-3 flex items-center gap-2 text-sm text-blue-600">
                        <Link className="w-4 h-4" />
                        <span>Meeting link available</span>
                        {session.isRecorded && (
                          <span className="text-gray-500">• Will be recorded</span>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    {isSessionLive(session) && (
                      <button
                        onClick={() => handleJoinSession(session)}
                        className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-lg text-sm transition-colors"
                      >
                        Join Live
                      </button>
                    )}
                    <button
                      onClick={() => handleEditSession(session)}
                      className="p-2 text-gray-400 hover:text-[#00d4aa] transition-colors"
                      title="Edit session"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteSession(session._id)}
                      className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                      title="Cancel session"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Session Form Modal */}
      {showSessionForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                {editingSession ? 'Edit Live Session' : 'Schedule New Live Session'}
              </h3>
            </div>
            <div className="p-6">
              <div className="text-center py-8">
                <Video className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h4 className="text-lg font-semibold text-gray-900 mb-2">Session Form Coming Soon</h4>
                <p className="text-gray-600 mb-4">
                  The session scheduling form will include fields for title, date/time, instructor,
                  meeting link, participant limits, and more.
                </p>
                <button
                  onClick={() => setShowSessionForm(false)}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LiveSessionManagement;

export default ContentManagement;
