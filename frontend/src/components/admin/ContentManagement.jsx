import { useState, useEffect } from 'react';
import {
  Plus,
  Search,
  Filter,
  Upload,
  Play,
  FileText,
  Video,
  Headphones,
  BookOpen,
  Edit,
  Trash2,
  Eye,
  Clock,
  Users,
  BarChart3,
  ChevronDown,
  ChevronRight,
  FolderOpen,
  File
} from 'lucide-react';
import { courseAPI } from '../../utils/api';

const ContentManagement = () => {
  const [courses, setCourses] = useState([]);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [lessons, setLessons] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedContentType, setSelectedContentType] = useState('all');
  const [showLessonForm, setShowLessonForm] = useState(false);
  const [editingLesson, setEditingLesson] = useState(null);
  const [expandedModules, setExpandedModules] = useState(new Set());

  const contentTypes = [
    { id: 'all', name: 'All Content', icon: FileText },
    { id: 'video', name: 'Videos', icon: Video },
    { id: 'audio', name: 'Audio', icon: Headphones },
    { id: 'text', name: 'Text Lessons', icon: BookOpen },
    { id: 'quiz', name: 'Quizzes', icon: BarChart3 },
    { id: 'practice', name: 'Practice', icon: Play }
  ];

  useEffect(() => {
    fetchCourses();
  }, []);

  useEffect(() => {
    if (selectedCourse) {
      fetchLessons(selectedCourse._id);
    }
  }, [selectedCourse]);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      const response = await courseAPI.getCourses({ status: 'published', limit: 100 });
      if (response.success) {
        const coursesData = response.data.courses || response.data;
        setCourses(coursesData);
        if (coursesData.length > 0) {
          setSelectedCourse(coursesData[0]);
        }
      }
    } catch (err) {
      console.error('Error fetching courses:', err);
      setError('Failed to load courses');
    } finally {
      setLoading(false);
    }
  };

  const fetchLessons = async (courseId) => {
    try {
      // TODO: Implement lesson API endpoint
      // For now, using mock data
      const mockLessons = [
        {
          _id: '1',
          title: 'Introduction to PTE Speaking',
          type: 'video',
          module: 'Getting Started',
          order: 1,
          duration: 480, // 8 minutes
          status: 'published',
          content: {
            video: {
              url: '/api/placeholder/video',
              duration: 480,
              thumbnail: '/api/placeholder/400/225'
            }
          }
        },
        {
          _id: '2',
          title: 'Read Aloud Techniques',
          type: 'video',
          module: 'Speaking Fundamentals',
          order: 2,
          duration: 720, // 12 minutes
          status: 'published',
          content: {
            video: {
              url: '/api/placeholder/video',
              duration: 720,
              thumbnail: '/api/placeholder/400/225'
            }
          }
        },
        {
          _id: '3',
          title: 'Practice Exercise: Read Aloud',
          type: 'practice',
          module: 'Speaking Fundamentals',
          order: 3,
          duration: 300, // 5 minutes
          status: 'published'
        },
        {
          _id: '4',
          title: 'Speaking Assessment Quiz',
          type: 'quiz',
          module: 'Speaking Fundamentals',
          order: 4,
          duration: 600, // 10 minutes
          status: 'draft'
        }
      ];
      setLessons(mockLessons);
    } catch (err) {
      console.error('Error fetching lessons:', err);
      setError('Failed to load lessons');
    }
  };

  const toggleModule = (moduleName) => {
    const newExpanded = new Set(expandedModules);
    if (newExpanded.has(moduleName)) {
      newExpanded.delete(moduleName);
    } else {
      newExpanded.add(moduleName);
    }
    setExpandedModules(newExpanded);
  };

  const getContentTypeIcon = (type) => {
    const typeMap = {
      video: Video,
      audio: Headphones,
      text: BookOpen,
      quiz: BarChart3,
      practice: Play
    };
    return typeMap[type] || FileText;
  };

  const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getStatusColor = (status) => {
    const colors = {
      published: 'bg-green-100 text-green-800',
      draft: 'bg-yellow-100 text-yellow-800',
      review: 'bg-blue-100 text-blue-800',
      archived: 'bg-gray-100 text-gray-800'
    };
    return colors[status] || colors.draft;
  };

  // Group lessons by module
  const groupedLessons = lessons.reduce((acc, lesson) => {
    const module = lesson.module || 'Uncategorized';
    if (!acc[module]) {
      acc[module] = [];
    }
    acc[module].push(lesson);
    return acc;
  }, {});

  // Filter lessons based on search and content type
  const filteredLessons = lessons.filter(lesson => {
    const matchesSearch = lesson.title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedContentType === 'all' || lesson.type === selectedContentType;
    return matchesSearch && matchesType;
  });

  const handleCreateLesson = () => {
    setEditingLesson(null);
    setShowLessonForm(true);
  };

  const handleEditLesson = (lesson) => {
    setEditingLesson(lesson);
    setShowLessonForm(true);
  };

  const handleDeleteLesson = async (lessonId) => {
    if (window.confirm('Are you sure you want to delete this lesson?')) {
      // TODO: Implement delete lesson API
      setLessons(lessons.filter(lesson => lesson._id !== lessonId));
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="lg:col-span-3 h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
          <h3 className="text-xl font-semibold text-red-900 mb-2">Error Loading Content</h3>
          <p className="text-red-800 mb-4">{error}</p>
          <button 
            onClick={fetchCourses}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Content Management</h1>
          <p className="text-gray-600 mt-1">
            Manage lessons, videos, quizzes, and learning materials
          </p>
        </div>
        <button
          onClick={handleCreateLesson}
          className="bg-[#00d4aa] hover:bg-[#00d4aa]/90 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
          disabled={!selectedCourse}
        >
          <Plus className="w-4 h-4" />
          Add Content
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Course Selector Sidebar */}
        <div className="bg-white rounded-xl p-6 shadow-sm border h-fit">
          <h3 className="font-semibold text-gray-900 mb-4">Select Course</h3>
          <div className="space-y-2">
            {courses.map((course) => (
              <button
                key={course._id}
                onClick={() => setSelectedCourse(course)}
                className={`w-full text-left p-3 rounded-lg transition-colors ${
                  selectedCourse?._id === course._id
                    ? 'bg-[#00d4aa]/10 border border-[#00d4aa] text-[#00d4aa]'
                    : 'hover:bg-gray-50 border border-transparent'
                }`}
              >
                <div className="font-medium text-sm">{course.title}</div>
                <div className="text-xs text-gray-500 mt-1">
                  {course.category} • {course.level}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Main Content Area */}
        <div className="lg:col-span-3 space-y-6">
          {selectedCourse ? (
            <>
              {/* Course Info & Filters */}
              <div className="bg-white rounded-xl p-6 shadow-sm border">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">{selectedCourse.title}</h2>
                    <p className="text-gray-600 text-sm mt-1">{selectedCourse.description}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-500">Total Lessons</div>
                    <div className="text-2xl font-bold text-gray-900">{lessons.length}</div>
                  </div>
                </div>

                {/* Search and Filters */}
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <input
                        type="text"
                        placeholder="Search lessons..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#00d4aa] focus:border-transparent"
                      />
                    </div>
                  </div>
                  <select
                    value={selectedContentType}
                    onChange={(e) => setSelectedContentType(e.target.value)}
                    className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-[#00d4aa] focus:border-transparent"
                  >
                    {contentTypes.map(type => (
                      <option key={type.id} value={type.id}>
                        {type.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Content Type Stats */}
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                {contentTypes.slice(1).map((type) => {
                  const Icon = type.icon;
                  const count = lessons.filter(lesson => lesson.type === type.id).length;
                  return (
                    <div key={type.id} className="bg-white rounded-lg p-4 shadow-sm border text-center">
                      <Icon className="w-6 h-6 text-[#00d4aa] mx-auto mb-2" />
                      <div className="text-2xl font-bold text-gray-900">{count}</div>
                      <div className="text-xs text-gray-600">{type.name}</div>
                    </div>
                  );
                })}
              </div>

              {/* Lessons List */}
              {selectedCourse && (
                <div className="bg-white rounded-xl shadow-sm border">
                  <div className="p-6 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-900">Course Content</h3>
                    <p className="text-gray-600 text-sm mt-1">Organized by modules and lessons</p>
                  </div>

                  <div className="p-6">
                    {Object.keys(groupedLessons).length === 0 ? (
                      <div className="text-center py-12">
                        <File className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">No Content Yet</h3>
                        <p className="text-gray-600 mb-4">Start building your course by adding lessons and materials</p>
                        <button
                          onClick={handleCreateLesson}
                          className="bg-[#00d4aa] hover:bg-[#00d4aa]/90 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                          Add Your First Lesson
                        </button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {Object.entries(groupedLessons).map(([moduleName, moduleLessons]) => (
                          <div key={moduleName} className="border border-gray-200 rounded-lg">
                            <button
                              onClick={() => toggleModule(moduleName)}
                              className="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors"
                            >
                              <div className="flex items-center gap-3">
                                {expandedModules.has(moduleName) ? (
                                  <ChevronDown className="w-4 h-4 text-gray-500" />
                                ) : (
                                  <ChevronRight className="w-4 h-4 text-gray-500" />
                                )}
                                <FolderOpen className="w-5 h-5 text-[#00d4aa]" />
                                <span className="font-medium text-gray-900">{moduleName}</span>
                                <span className="text-sm text-gray-500">({moduleLessons.length} lessons)</span>
                              </div>
                              <div className="text-sm text-gray-500">
                                {Math.floor(moduleLessons.reduce((total, lesson) => total + (lesson.duration || 0), 0) / 60)} min total
                              </div>
                            </button>

                            {expandedModules.has(moduleName) && (
                              <div className="border-t border-gray-200">
                                {moduleLessons
                                  .sort((a, b) => a.order - b.order)
                                  .map((lesson) => {
                                    const Icon = getContentTypeIcon(lesson.type);
                                    return (
                                      <div key={lesson._id} className="flex items-center justify-between p-4 hover:bg-gray-50 border-b border-gray-100 last:border-b-0">
                                        <div className="flex items-center gap-3">
                                          <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                                            <Icon className="w-4 h-4 text-gray-600" />
                                          </div>
                                          <div>
                                            <div className="font-medium text-gray-900">{lesson.title}</div>
                                            <div className="flex items-center gap-2 text-sm text-gray-500">
                                              <Clock className="w-3 h-3" />
                                              {formatDuration(lesson.duration || 0)}
                                              <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(lesson.status)}`}>
                                                {lesson.status}
                                              </span>
                                            </div>
                                          </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                          <button
                                            onClick={() => handleEditLesson(lesson)}
                                            className="p-2 text-gray-400 hover:text-[#00d4aa] transition-colors"
                                            title="Edit lesson"
                                          >
                                            <Edit className="w-4 h-4" />
                                          </button>
                                          <button
                                            onClick={() => handleDeleteLesson(lesson._id)}
                                            className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                                            title="Delete lesson"
                                          >
                                            <Trash2 className="w-4 h-4" />
                                          </button>
                                        </div>
                                      </div>
                                    );
                                  })}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="bg-white rounded-xl p-12 shadow-sm border text-center">
              <FolderOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Select a Course</h3>
              <p className="text-gray-600">Choose a course from the sidebar to manage its content</p>
            </div>
          )}
        </div>
      </div>

      {/* Lesson Form Modal - TODO: Implement LessonForm component */}
      {showLessonForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                {editingLesson ? 'Edit Lesson' : 'Add New Lesson'}
              </h3>
            </div>
            <div className="p-6">
              <div className="text-center py-8">
                <Upload className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h4 className="text-lg font-semibold text-gray-900 mb-2">Lesson Form Coming Soon</h4>
                <p className="text-gray-600 mb-4">
                  The lesson creation form will include fields for title, content type, video upload,
                  quiz creation, and more.
                </p>
                <button
                  onClick={() => setShowLessonForm(false)}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContentManagement;
