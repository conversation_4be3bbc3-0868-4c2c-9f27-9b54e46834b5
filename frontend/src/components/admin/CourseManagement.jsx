import { useState, useEffect } from 'react';
import {
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  MoreVertical,
  Grid,
  List,
  Trash2,
  Edit,
  Eye,
  CheckSquare,
  Square
} from 'lucide-react';
import CourseCard from '../courses/CourseCard';
import CourseForm from '../courses/CourseForm';
import useCourses from '../../hooks/useCourses';

const CourseManagement = () => {
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCourses, setSelectedCourses] = useState(new Set());
  const [filters, setFilters] = useState({
    status: '',
    category: '',
    level: '',
    pricing: ''
  });
  const [showFilters, setShowFilters] = useState(false);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [showCourseForm, setShowCourseForm] = useState(false);
  const [editingCourse, setEditingCourse] = useState(null);

  // Use the custom hook
  const {
    courses,
    loading,
    error,
    pagination,
    fetchCourses,
    createCourse,
    updateCourse,
    deleteCourse,
    toggleCourseStatus,
    bulkUpdateCourses,
    bulkDeleteCourses
  } = useCourses();

  // Filter courses based on search and filters
  const filteredCourses = courses.filter(course => {
    // Safety check - ensure course has required properties
    if (!course || !course.title) {
      console.warn('Invalid course object:', course);
      return false;
    }

    const matchesSearch = course.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.instructor?.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.instructor?.lastName?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = !filters.status || course.status === filters.status;
    const matchesCategory = !filters.category || course.category === filters.category;
    const matchesLevel = !filters.level || course.level === filters.level;
    const matchesPricing = !filters.pricing ||
      (filters.pricing === 'free' && course.pricing?.price === 0) ||
      (filters.pricing === 'paid' && course.pricing?.price > 0);

    return matchesSearch && matchesStatus && matchesCategory && matchesLevel && matchesPricing;
  });

  // Handle course selection
  const handleCourseSelect = (courseId, isSelected) => {
    const newSelected = new Set(selectedCourses);
    if (isSelected) {
      newSelected.add(courseId);
    } else {
      newSelected.delete(courseId);
    }
    setSelectedCourses(newSelected);
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedCourses.size === filteredCourses.length) {
      setSelectedCourses(new Set());
    } else {
      setSelectedCourses(new Set(filteredCourses.map(course => course._id)));
    }
  };

  // Handle bulk actions
  const handleBulkDelete = async () => {
    if (window.confirm(`Are you sure you want to delete ${selectedCourses.size} courses?`)) {
      try {
        await bulkDeleteCourses(Array.from(selectedCourses));
        setSelectedCourses(new Set());
        setShowBulkActions(false);
      } catch (error) {
        console.error('Failed to delete courses:', error);
      }
    }
  };

  const handleBulkStatusUpdate = async (status) => {
    try {
      await bulkUpdateCourses(Array.from(selectedCourses), { status });
      setSelectedCourses(new Set());
      setShowBulkActions(false);
    } catch (error) {
      console.error('Failed to update course status:', error);
    }
  };

  // Handle individual course actions
  const handleEditCourse = (course) => {
    setEditingCourse(course);
    setShowCourseForm(true);
  };

  const handleViewCourse = (course) => {
    // TODO: Open course preview/details
    console.log('View course:', course);
  };

  const handleDeleteCourse = async (courseId) => {
    if (window.confirm('Are you sure you want to delete this course?')) {
      try {
        await deleteCourse(courseId);
      } catch (error) {
        console.error('Failed to delete course:', error);
      }
    }
  };

  const handleCreateCourse = () => {
    setEditingCourse(null);
    setShowCourseForm(true);
  };

  // Handle course form save
  const handleSaveCourse = async (courseData) => {
    try {
      if (editingCourse) {
        // Update existing course
        await updateCourse(editingCourse._id, courseData);
        console.log('Course updated successfully');
      } else {
        // Create new course
        const newCourse = await createCourse(courseData);
        console.log('Course created successfully:', newCourse);
      }
      // Success - form will close automatically via CourseForm's onSave handler
    } catch (error) {
      // Error will be handled by the CourseForm component
      console.error('Error saving course:', error);
      throw error; // Re-throw to let CourseForm handle the error display
    }
  };

  // Handle course form close
  const handleCloseCourseForm = () => {
    setShowCourseForm(false);
    setEditingCourse(null);
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
      status: '',
      category: '',
      level: '',
      pricing: ''
    });
    setSearchTerm('');
  };

  // Show bulk actions when courses are selected
  useEffect(() => {
    setShowBulkActions(selectedCourses.size > 0);
  }, [selectedCourses]);

  if (loading && courses.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Error boundary for rendering issues
  if (error && courses.length === 0) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-red-800 mb-2">Error Loading Courses</h3>
          <p className="text-red-600">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-3 btn-primary"
          >
            Reload Page
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Course Management</h1>
          <p className="text-gray-600 mt-1">
            Manage your courses, track performance, and analyze student engagement
          </p>
        </div>
        <button
          onClick={handleCreateCourse}
          className="btn-primary flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Create Course
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Courses</p>
              <p className="text-2xl font-bold text-gray-900">{courses.length}</p>
            </div>
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Grid className="w-5 h-5 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Published</p>
              <p className="text-2xl font-bold text-green-600">
                {courses.filter(c => c.status === 'published').length}
              </p>
            </div>
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <Eye className="w-5 h-5 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Draft</p>
              <p className="text-2xl font-bold text-yellow-600">
                {courses.filter(c => c.status === 'draft').length}
              </p>
            </div>
            <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Edit className="w-5 h-5 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-primary">
                ${courses.reduce((sum, c) => sum + (c.stats?.totalRevenue || 0), 0).toLocaleString()}
              </p>
            </div>
            <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
              <Download className="w-5 h-5 text-primary" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search courses by title, description, or instructor..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
          </div>

          {/* Filter Toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`btn-secondary flex items-center gap-2 ${showFilters ? 'bg-primary text-white' : ''}`}
          >
            <Filter className="w-4 h-4" />
            Filters
          </button>

          {/* View Mode Toggle */}
          <div className="flex border border-gray-300 rounded-lg overflow-hidden">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 ${viewMode === 'grid' ? 'bg-primary text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 ${viewMode === 'list' ? 'bg-primary text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                className="form-select"
              >
                <option value="">All Status</option>
                <option value="published">Published</option>
                <option value="draft">Draft</option>
                <option value="review">Under Review</option>
                <option value="archived">Archived</option>
              </select>

              <select
                value={filters.category}
                onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
                className="form-select"
              >
                <option value="">All Categories</option>
                <option value="speaking">Speaking</option>
                <option value="writing">Writing</option>
                <option value="reading">Reading</option>
                <option value="listening">Listening</option>
                <option value="mock-tests">Mock Tests</option>
                <option value="comprehensive">Comprehensive</option>
              </select>

              <select
                value={filters.level}
                onChange={(e) => setFilters(prev => ({ ...prev, level: e.target.value }))}
                className="form-select"
              >
                <option value="">All Levels</option>
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
                <option value="all">All Levels</option>
              </select>

              <select
                value={filters.pricing}
                onChange={(e) => setFilters(prev => ({ ...prev, pricing: e.target.value }))}
                className="form-select"
              >
                <option value="">All Pricing</option>
                <option value="free">Free</option>
                <option value="paid">Paid</option>
              </select>
            </div>

            <div className="flex justify-end mt-4">
              <button
                onClick={clearFilters}
                className="text-sm text-gray-600 hover:text-gray-900"
              >
                Clear all filters
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Bulk Actions */}
      {showBulkActions && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-blue-900">
                {selectedCourses.size} course{selectedCourses.size !== 1 ? 's' : ''} selected
              </span>
              <button
                onClick={() => setSelectedCourses(new Set())}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                Clear selection
              </button>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => handleBulkStatusUpdate('published')}
                className="btn-secondary text-sm"
              >
                Publish
              </button>
              <button
                onClick={() => handleBulkStatusUpdate('draft')}
                className="btn-secondary text-sm"
              >
                Unpublish
              </button>
              <button
                onClick={handleBulkDelete}
                className="btn-danger text-sm"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Course List Header (for list view) */}
      {viewMode === 'list' && (
        <div className="bg-white border border-gray-200 rounded-lg">
          <div className="px-4 py-3 border-b border-gray-200">
            <div className="flex items-center gap-4">
              <button
                onClick={handleSelectAll}
                className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900"
              >
                {selectedCourses.size === filteredCourses.length && filteredCourses.length > 0 ? (
                  <CheckSquare className="w-4 h-4" />
                ) : (
                  <Square className="w-4 h-4" />
                )}
                Select All
              </button>
              <span className="text-sm text-gray-500">
                {filteredCourses.length} course{filteredCourses.length !== 1 ? 's' : ''}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Course Grid/List */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {filteredCourses.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Grid className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No courses found</h3>
          <p className="text-gray-600 mb-4">
            {searchTerm || Object.values(filters).some(f => f)
              ? 'Try adjusting your search or filters'
              : 'Get started by creating your first course'
            }
          </p>
          {!searchTerm && !Object.values(filters).some(f => f) && (
            <button
              onClick={handleCreateCourse}
              className="btn-primary"
            >
              Create Your First Course
            </button>
          )}
        </div>
      ) : (
        <div className={
          viewMode === 'grid'
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
            : 'space-y-4'
        }>
          {filteredCourses.map((course) => (
            <CourseCard
              key={course._id}
              course={course}
              onEdit={handleEditCourse}
              onDelete={handleDeleteCourse}
              onToggleStatus={toggleCourseStatus}
              onView={handleViewCourse}
              isSelected={selectedCourses.has(course._id)}
              onSelect={handleCourseSelect}
              variant="admin"
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
            {pagination.total} courses
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => fetchCourses({ page: pagination.page - 1 })}
              disabled={pagination.page === 1}
              className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <span className="px-3 py-1 bg-primary text-white rounded">
              {pagination.page}
            </span>
            <button
              onClick={() => fetchCourses({ page: pagination.page + 1 })}
              disabled={pagination.page === pagination.totalPages}
              className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* Course Form Modal */}
      <CourseForm
        course={editingCourse}
        isOpen={showCourseForm}
        onClose={handleCloseCourseForm}
        onSave={handleSaveCourse}
        mode={editingCourse ? 'edit' : 'create'}
      />
    </div>
  );
};

export default CourseManagement;
