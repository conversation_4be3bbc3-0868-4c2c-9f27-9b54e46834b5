import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { courseAPI, userAPI } from '../../utils/api';
import {
  BookOpen,
  Clock,
  Trophy,
  TrendingUp,
  Play,
  Star,
  Calendar,
  Target,
  Award,
  ChevronRight
} from 'lucide-react';

const StudentDashboard = () => {
  const { user } = useAuth();

  const [stats, setStats] = useState({
    coursesEnrolled: 0,
    hoursStudied: 0,
    testsCompleted: 0,
    averageScore: 0
  });
  const [recentCourses, setRecentCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch real student data
  useEffect(() => {
    fetchStudentData();
  }, [user]);

  const fetchStudentData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch user's enrolled courses
      const coursesResponse = await courseAPI.getCourses({
        enrolled: true,
        limit: 3,
        sortBy: 'lastAccessed'
      });

      if (coursesResponse.success) {
        const courses = coursesResponse.data.courses || coursesResponse.data;
        setRecentCourses(courses.map(course => ({
          id: course._id || course.id,
          title: course.title,
          progress: course.progress || Math.floor(Math.random() * 100), // TODO: Get real progress
          lastAccessed: course.lastAccessed || 'Recently',
          difficulty: course.level || 'Intermediate'
        })));

        // Update stats based on real data
        setStats(prevStats => ({
          ...prevStats,
          coursesEnrolled: courses.length
        }));
      }

      // TODO: Fetch real user activity data when available
      // For now, use some calculated values based on available data
      setStats(prevStats => ({
        ...prevStats,
        hoursStudied: user.studyTime?.total || 0,
        testsCompleted: user.testResults?.length || 0,
        averageScore: user.testResults?.length > 0
          ? Math.round(user.testResults.reduce((sum, test) => sum + test.score, 0) / user.testResults.length)
          : 0
      }));

    } catch (err) {
      console.error('Error fetching student data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const [practiceModules] = useState([
    { name: 'Read Aloud', icon: '🗣️', score: 'AI Score', color: 'bg-blue-500' },
    { name: 'Repeat Sentence', icon: '🔄', score: 'AI Score', color: 'bg-green-500' },
    { name: 'Describe Image', icon: '🖼️', score: 'AI Score', color: 'bg-purple-500' },
    { name: 'Write Essay', icon: '✍️', score: 'AI Score', color: 'bg-orange-500' },
    { name: 'Summarize Text', icon: '📝', score: 'AI Score', color: 'bg-pink-500' },
    { name: 'Multiple Choice', icon: '☑️', score: 'Practice', color: 'bg-indigo-500' }
  ]);

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-[#00d4aa] to-[#4ecdc4] rounded-2xl p-8 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              Welcome back, {user?.firstName || 'Student'}!
            </h1>
            <p className="text-white/80 text-lg">
              Continue your PTE preparation journey
            </p>
          </div>
          <div className="hidden md:block">
            <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
              <Trophy className="w-12 h-12 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Courses Enrolled</p>
              <p className="text-2xl font-bold text-gray-900">{stats.coursesEnrolled}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <BookOpen className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Hours Studied</p>
              <p className="text-2xl font-bold text-gray-900">{stats.hoursStudied}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Tests Completed</p>
              <p className="text-2xl font-bold text-gray-900">{stats.testsCompleted}</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Target className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Average Score</p>
              <p className="text-2xl font-bold text-gray-900">{stats.averageScore}%</p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Quick Practice */}
      <div className="bg-white rounded-xl p-6 shadow-sm border">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900">Quick Practice</h2>
          <Link to="/practice" className="text-[#00d4aa] hover:text-[#00d4aa]/80 font-medium">
            View All
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {practiceModules.map((module, index) => (
            <div
              key={index}
              className="practice-module cursor-pointer group"
            >
              <div className="flex items-center gap-4">
                <div className={`w-12 h-12 ${module.color} rounded-lg flex items-center justify-center text-white text-xl`}>
                  {module.icon}
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 group-hover:text-[#00d4aa] transition-colors">
                    {module.name}
                  </h3>
                  <p className="text-sm text-gray-600">{module.score}</p>
                </div>
                <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-[#00d4aa] transition-colors" />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Courses */}
      <div className="bg-white rounded-xl p-6 shadow-sm border">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900">Continue Learning</h2>
          <Link to="/courses" className="text-[#00d4aa] hover:text-[#00d4aa]/80 font-medium">
            View All Courses
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {recentCourses.map((course) => (
            <div key={course.id} className="course-card group cursor-pointer">
              <div className="aspect-video bg-gradient-to-br from-[#00d4aa]/20 to-[#4ecdc4]/20 rounded-lg mb-4 flex items-center justify-center">
                <Play className="w-12 h-12 text-[#00d4aa]" />
              </div>

              <div className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    course.difficulty === 'Beginner' ? 'bg-green-100 text-green-800' :
                    course.difficulty === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {course.difficulty}
                  </span>
                </div>

                <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-primary transition-colors">
                  {course.title}
                </h3>

                <div className="mb-3">
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                    <span>Progress</span>
                    <span>{course.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${course.progress}%` }}
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>Last accessed: {course.lastAccessed}</span>
                  <ChevronRight className="w-4 h-4" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Study Streak */}
      <div className="bg-white rounded-xl p-6 shadow-sm border">
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center">
            <Award className="w-8 h-8 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900">7-Day Study Streak!</h3>
            <p className="text-gray-600">Keep up the great work! You're on fire 🔥</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentDashboard;
