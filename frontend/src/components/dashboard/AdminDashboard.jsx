import { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { Link } from 'react-router-dom';
import {
  Users,
  BookOpen,
  BarChart3,
  DollarSign,
  TrendingUp,
  UserCheck,
  AlertCircle,
  Calendar,
  Settings,
  Plus
} from 'lucide-react';

const AdminDashboard = () => {
  const { user } = useAuth();

  // Mock data for admin dashboard
  const [stats, setStats] = useState({
    totalUsers: 1247,
    totalCourses: 45,
    totalRevenue: 89750,
    activeUsers: 892,
    newUsersToday: 23,
    coursesCompleted: 156,
    pendingApprovals: 8,
    systemAlerts: 3
  });

  const [recentActivities, setRecentActivities] = useState([
    { id: 1, type: 'user_registration', message: 'New user registered: <EMAIL>', time: '2 minutes ago' },
    { id: 2, type: 'course_completion', message: 'Course "Advanced Speaking" completed by <PERSON>', time: '15 minutes ago' },
    { id: 3, type: 'payment', message: 'Premium subscription purchased by <PERSON>', time: '1 hour ago' },
    { id: 4, type: 'course_creation', message: 'New course "PTE Writing Mastery" created', time: '2 hours ago' },
    { id: 5, type: 'system', message: 'System backup completed successfully', time: '3 hours ago' }
  ]);

  const [topCourses, setTopCourses] = useState([
    { id: 1, title: 'PTE Speaking Fundamentals', enrollments: 234, rating: 4.8 },
    { id: 2, title: 'Advanced Writing Techniques', enrollments: 189, rating: 4.7 },
    { id: 3, title: 'Reading Comprehension Mastery', enrollments: 156, rating: 4.9 },
    { id: 4, title: 'Listening Skills Enhancement', enrollments: 143, rating: 4.6 }
  ]);

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-8 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              Welcome back, {user?.firstName || 'Admin'}!
            </h1>
            <p className="text-white/80 text-lg">
              Manage your PTE platform and monitor performance
            </p>
          </div>
          <div className="hidden md:block">
            <Settings className="w-16 h-16 text-white/30" />
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalUsers.toLocaleString()}</p>
              <p className="text-sm text-green-600 mt-1">+{stats.newUsersToday} today</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Courses</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalCourses}</p>
              <p className="text-sm text-blue-600 mt-1">Active courses</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <BookOpen className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">${stats.totalRevenue.toLocaleString()}</p>
              <p className="text-sm text-green-600 mt-1">+12% this month</p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Users</p>
              <p className="text-2xl font-bold text-gray-900">{stats.activeUsers}</p>
              <p className="text-sm text-green-600 mt-1">Online now</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <UserCheck className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="flex items-center gap-3 p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
            <Plus className="w-5 h-5 text-blue-600" />
            <span className="font-medium text-blue-900">Create New Course</span>
          </button>
          <Link
            to="/admin/users"
            className="flex items-center gap-3 p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors"
          >
            <Users className="w-5 h-5 text-green-600" />
            <span className="font-medium text-green-900">Manage Users</span>
          </Link>
          <Link
            to="/admin/analytics"
            className="flex items-center gap-3 p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors"
          >
            <BarChart3 className="w-5 h-5 text-purple-600" />
            <span className="font-medium text-purple-900">View Analytics</span>
          </Link>
        </div>
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Activities */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Activities</h2>
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-start gap-3 p-3 hover:bg-gray-50 rounded-lg">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">{activity.message}</p>
                  <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Courses */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Top Performing Courses</h2>
          <div className="space-y-4">
            {topCourses.map((course) => (
              <div key={course.id} className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg">
                <div>
                  <h3 className="font-medium text-gray-900">{course.title}</h3>
                  <p className="text-sm text-gray-600">{course.enrollments} enrollments</p>
                </div>
                <div className="text-right">
                  <div className="flex items-center gap-1">
                    <span className="text-yellow-500">★</span>
                    <span className="text-sm font-medium">{course.rating}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* System Alerts */}
      {stats.systemAlerts > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
          <div className="flex items-center gap-3">
            <AlertCircle className="w-6 h-6 text-yellow-600" />
            <div>
              <h3 className="font-semibold text-yellow-900">System Alerts</h3>
              <p className="text-yellow-800">You have {stats.systemAlerts} system alerts that require attention.</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;
