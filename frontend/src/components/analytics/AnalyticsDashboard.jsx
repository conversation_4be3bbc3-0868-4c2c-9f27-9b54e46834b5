import { useState, useEffect } from 'react';
import { analyticsAPI } from '../../utils/api';
import {
  BarChart3,
  Users,
  BookOpen,
  DollarSign,
  TrendingUp,
  Calendar,
  Activity,
  Target
} from 'lucide-react';
import StatsCard from './StatsCard';
import ChartContainer from './ChartContainer';
import TimeframeSelector from './TimeframeSelector';
import LoadingSpinner from './LoadingSpinner';
import ErrorMessage from './ErrorMessage';

const AnalyticsDashboard = () => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timeframe, setTimeframe] = useState('30d');

  useEffect(() => {
    fetchAnalytics();
  }, [timeframe]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await analyticsAPI.getDashboardAnalytics({ timeframe });
      setAnalytics(response.data);
    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <LoadingSpinner size="lg" text="Loading analytics data..." className="min-h-96" />;
  }

  if (error) {
    return <ErrorMessage message={error} onRetry={fetchAnalytics} className="min-h-96" />;
  }

  const { overview } = analytics;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Monitor your platform performance and user engagement
          </p>
        </div>
        <TimeframeSelector value={timeframe} onChange={setTimeframe} />
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Users"
          value={overview.users.total.toLocaleString()}
          change={`+${overview.users.new} new`}
          changeType="positive"
          icon={Users}
          color="blue"
        />
        <StatsCard
          title="Active Courses"
          value={overview.courses.published.toLocaleString()}
          change={`+${overview.courses.new} this period`}
          changeType="positive"
          icon={BookOpen}
          color="green"
        />
        <StatsCard
          title="Total Revenue"
          value={`$${overview.revenue.total.toLocaleString()}`}
          change={`$${overview.revenue.monthly}/month`}
          changeType="positive"
          icon={DollarSign}
          color="yellow"
        />
        <StatsCard
          title="Completion Rate"
          value={`${overview.enrollments.completionRate}%`}
          change={`${overview.enrollments.completed} completed`}
          changeType="positive"
          icon={Target}
          color="purple"
        />
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* User Role Distribution */}
        <ChartContainer
          title="User Distribution"
          subtitle="Users by role"
          icon={Users}
        >
          <div className="space-y-4">
            {Object.entries(overview.users.roleDistribution).map(([role, count]) => (
              <div key={role} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${
                    role === 'student' ? 'bg-blue-500' :
                    role === 'instructor' ? 'bg-green-500' : 'bg-purple-500'
                  }`}></div>
                  <span className="capitalize font-medium">{role}s</span>
                </div>
                <span className="text-lg font-semibold">{count}</span>
              </div>
            ))}
          </div>
        </ChartContainer>

        {/* Course Categories */}
        <ChartContainer
          title="Course Performance"
          subtitle="By category"
          icon={BookOpen}
        >
          <div className="space-y-4">
            {overview.courses.categoryDistribution.map((category) => (
              <div key={category._id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="capitalize font-medium">{category._id}</span>
                  <span className="text-sm text-gray-600">
                    {category.count} courses
                  </span>
                </div>
                <div className="flex items-center gap-4">
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full"
                      style={{
                        width: `${(category.totalEnrollments / Math.max(...overview.courses.categoryDistribution.map(c => c.totalEnrollments))) * 100}%`
                      }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium">
                    {category.totalEnrollments} enrollments
                  </span>
                </div>
              </div>
            ))}
          </div>
        </ChartContainer>

        {/* Activity Stats */}
        <ChartContainer
          title="Learning Activity"
          subtitle="Recent engagement"
          icon={Activity}
        >
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {overview.activity.lessonCompletions}
              </div>
              <div className="text-sm text-blue-800">Lessons Completed</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {overview.activity.averageTimeSpent}m
              </div>
              <div className="text-sm text-green-800">Avg. Time Spent</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {overview.activity.studyStreaks.average}
              </div>
              <div className="text-sm text-purple-800">Avg. Study Streak</div>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">
                {overview.activity.averageScore}%
              </div>
              <div className="text-sm text-yellow-800">Avg. Score</div>
            </div>
          </div>
        </ChartContainer>

        {/* Revenue Breakdown */}
        <ChartContainer
          title="Revenue Breakdown"
          subtitle="Income sources"
          icon={DollarSign}
        >
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <span className="font-medium">Course Sales</span>
              <span className="text-lg font-bold text-green-600">
                ${overview.revenue.total.toLocaleString()}
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <span className="font-medium">Subscriptions</span>
              <span className="text-lg font-bold text-blue-600">
                ${overview.revenue.monthly.toLocaleString()}/mo
              </span>
            </div>
            {overview.revenue.subscriptionBreakdown.map((sub) => (
              <div key={sub._id} className="flex items-center justify-between pl-6">
                <span className="text-sm capitalize">{sub._id} Plan</span>
                <span className="text-sm font-medium">{sub.count} users</span>
              </div>
            ))}
          </div>
        </ChartContainer>
      </div>

      {/* Test Results Summary */}
      {overview.activity.testResults.length > 0 && (
        <ChartContainer
          title="Test Performance"
          subtitle="Average scores by section"
          icon={BarChart3}
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {overview.activity.testResults.map((test) => (
              <div key={test._id} className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-xl font-bold text-gray-900">
                  {Math.round(test.averageScore)}%
                </div>
                <div className="text-sm text-gray-600 capitalize">{test._id}</div>
                <div className="text-xs text-gray-500">{test.count} tests</div>
              </div>
            ))}
          </div>
        </ChartContainer>
      )}
    </div>
  );
};

export default AnalyticsDashboard;
