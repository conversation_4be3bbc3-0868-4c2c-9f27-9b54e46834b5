const LoadingSpinner = ({ size = 'md', text = 'Loading...', className = '' }) => {
  const sizeClasses = {
    sm: 'loading-sm',
    md: 'loading-md',
    lg: 'loading-lg'
  };

  return (
    <div className={`flex flex-col items-center justify-center p-8 ${className}`}>
      <div className={`loading loading-spinner ${sizeClasses[size]} text-blue-600`}></div>
      {text && (
        <p className="text-gray-600 mt-4 text-sm">{text}</p>
      )}
    </div>
  );
};

export default LoadingSpinner;
