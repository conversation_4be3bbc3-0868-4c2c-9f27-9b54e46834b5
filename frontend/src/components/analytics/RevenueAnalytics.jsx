import { useState, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js';
import { Bar, Doughnut } from 'react-chartjs-2';
import { analyticsAPI } from '../../utils/api';
import ChartContainer from './ChartContainer';
import StatsCard from './StatsCard';
import TimeframeSelector from './TimeframeSelector';
import { DollarSign, TrendingUp, CreditCard, Users } from 'lucide-react';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

const RevenueAnalytics = () => {
  const [revenue, setRevenue] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timeframe, setTimeframe] = useState('30d');

  useEffect(() => {
    fetchRevenueAnalytics();
  }, [timeframe]);

  const fetchRevenueAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await analyticsAPI.getRevenueAnalytics({ timeframe });
      setRevenue(response.data);
    } catch (err) {
      console.error('Error fetching revenue analytics:', err);
      setError('Failed to load revenue analytics');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center min-h-96">
          <div className="loading loading-spinner loading-lg"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="alert alert-error">
          <span>{error}</span>
          <button className="btn btn-sm" onClick={fetchRevenueAnalytics}>
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Prepare chart data for course revenue by category
  const courseRevenueData = {
    labels: revenue.breakdown.courses.map(course => 
      course._id.charAt(0).toUpperCase() + course._id.slice(1)
    ),
    datasets: [
      {
        label: 'Revenue ($)',
        data: revenue.breakdown.courses.map(course => course.totalRevenue),
        backgroundColor: [
          'rgba(59, 130, 246, 0.8)',
          'rgba(16, 185, 129, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(239, 68, 68, 0.8)',
          'rgba(139, 92, 246, 0.8)',
          'rgba(236, 72, 153, 0.8)'
        ],
        borderColor: [
          'rgb(59, 130, 246)',
          'rgb(16, 185, 129)',
          'rgb(245, 158, 11)',
          'rgb(239, 68, 68)',
          'rgb(139, 92, 246)',
          'rgb(236, 72, 153)'
        ],
        borderWidth: 1
      }
    ]
  };

  // Prepare subscription revenue pie chart
  const subscriptionData = {
    labels: revenue.breakdown.subscriptions.map(sub => 
      sub.plan.charAt(0).toUpperCase() + sub.plan.slice(1) + ' Plan'
    ),
    datasets: [
      {
        data: revenue.breakdown.subscriptions.map(sub => sub.monthlyRevenue),
        backgroundColor: [
          'rgba(34, 197, 94, 0.8)',
          'rgba(59, 130, 246, 0.8)',
          'rgba(168, 85, 247, 0.8)'
        ],
        borderColor: [
          'rgb(34, 197, 94)',
          'rgb(59, 130, 246)',
          'rgb(168, 85, 247)'
        ],
        borderWidth: 2
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.label}: $${context.parsed.toLocaleString()}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return '$' + value.toLocaleString();
          }
        }
      }
    }
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = ((context.parsed / total) * 100).toFixed(1);
            return `${context.label}: $${context.parsed.toLocaleString()} (${percentage}%)`;
          }
        }
      }
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Revenue Analytics</h1>
          <p className="text-gray-600 mt-1">
            Track your platform's financial performance
          </p>
        </div>
        <TimeframeSelector value={timeframe} onChange={setTimeframe} />
      </div>

      {/* Revenue Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Revenue"
          value={`$${revenue.summary.totalRevenue.toLocaleString()}`}
          change="All time"
          changeType="neutral"
          icon={DollarSign}
          color="green"
        />
        <StatsCard
          title="Course Revenue"
          value={`$${revenue.summary.courseRevenue.toLocaleString()}`}
          change={`${revenue.breakdown.courses.length} categories`}
          changeType="positive"
          icon={TrendingUp}
          color="blue"
        />
        <StatsCard
          title="Subscription Revenue"
          value={`$${revenue.summary.subscriptionRevenue.toLocaleString()}`}
          change="Monthly recurring"
          changeType="positive"
          icon={CreditCard}
          color="purple"
        />
        <StatsCard
          title="Active Subscribers"
          value={revenue.breakdown.subscriptions.reduce((total, sub) => total + sub.subscribers, 0)}
          change="Paying users"
          changeType="positive"
          icon={Users}
          color="yellow"
        />
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Course Revenue by Category */}
        <ChartContainer
          title="Course Revenue by Category"
          subtitle="Revenue breakdown by course categories"
          icon={DollarSign}
        >
          <div className="h-80">
            <Bar data={courseRevenueData} options={chartOptions} />
          </div>
        </ChartContainer>

        {/* Subscription Revenue Distribution */}
        <ChartContainer
          title="Subscription Revenue Distribution"
          subtitle="Monthly recurring revenue by plan"
          icon={CreditCard}
        >
          <div className="h-80">
            <Doughnut data={subscriptionData} options={doughnutOptions} />
          </div>
        </ChartContainer>
      </div>

      {/* Detailed Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Course Revenue Details */}
        <ChartContainer
          title="Course Revenue Details"
          subtitle="Performance by category"
          icon={DollarSign}
        >
          <div className="overflow-x-auto">
            <table className="table table-zebra w-full">
              <thead>
                <tr>
                  <th>Category</th>
                  <th>Courses</th>
                  <th>Revenue</th>
                  <th>Avg/Course</th>
                </tr>
              </thead>
              <tbody>
                {revenue.breakdown.courses.map((course) => (
                  <tr key={course._id}>
                    <td className="capitalize font-medium">{course._id}</td>
                    <td>{course.courseCount}</td>
                    <td className="font-semibold text-green-600">
                      ${course.totalRevenue.toLocaleString()}
                    </td>
                    <td>${Math.round(course.averageRevenue).toLocaleString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </ChartContainer>

        {/* Subscription Details */}
        <ChartContainer
          title="Subscription Plan Details"
          subtitle="Subscriber breakdown and revenue"
          icon={CreditCard}
        >
          <div className="space-y-4">
            {revenue.breakdown.subscriptions.map((sub) => (
              <div key={sub.plan} className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold capitalize">{sub.plan} Plan</h4>
                  <span className="text-lg font-bold text-green-600">
                    ${sub.monthlyRevenue.toLocaleString()}/mo
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <span>{sub.subscribers} subscribers</span>
                  <span>${sub.annualRevenue.toLocaleString()}/year</span>
                </div>
              </div>
            ))}
          </div>
        </ChartContainer>
      </div>
    </div>
  );
};

export default RevenueAnalytics;
