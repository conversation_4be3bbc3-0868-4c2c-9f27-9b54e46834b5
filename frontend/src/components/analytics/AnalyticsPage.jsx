import { useState } from 'react';
import { BarChart3, TrendingUp, Users, BookOpen, DollarSign } from 'lucide-react';
import AnalyticsDashboard from './AnalyticsDashboard';
import UserTrendsChart from './UserTrendsChart';
import CoursePerformanceTable from './CoursePerformanceTable';
import RevenueAnalytics from './RevenueAnalytics';

const AnalyticsPage = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: BarChart3,
      component: AnalyticsDashboard
    },
    {
      id: 'users',
      label: 'User Analytics',
      icon: Users,
      component: UserTrendsChart
    },
    {
      id: 'courses',
      label: 'Course Performance',
      icon: BookOpen,
      component: CoursePerformanceTable
    },
    {
      id: 'revenue',
      label: 'Revenue Analytics',
      icon: DollarSign,
      component: RevenueAnalytics
    }
  ];

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || AnalyticsDashboard;

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100">
        <div className="flex overflow-x-auto">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-3 px-6 py-4 font-medium text-sm whitespace-nowrap border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 bg-blue-50'
                    : 'border-transparent text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <Icon className="w-5 h-5" />
                {tab.label}
              </button>
            );
          })}
        </div>
      </div>

      {/* Tab Content */}
      <div className="min-h-96">
        <ActiveComponent />
      </div>
    </div>
  );
};

export default AnalyticsPage;
