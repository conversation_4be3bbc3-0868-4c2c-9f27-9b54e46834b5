import { useState, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  BarElement
} from 'chart.js';
import { Line, Bar } from 'react-chartjs-2';
import { analyticsAPI } from '../../utils/api';
import ChartContainer from './ChartContainer';
import TimeframeSelector from './TimeframeSelector';
import { TrendingUp, Users } from 'lucide-react';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const UserTrendsChart = () => {
  const [trends, setTrends] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timeframe, setTimeframe] = useState('30d');
  const [chartType, setChartType] = useState('line');

  useEffect(() => {
    fetchTrends();
  }, [timeframe]);

  const fetchTrends = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await analyticsAPI.getUserTrends({ timeframe });
      setTrends(response.data);
    } catch (err) {
      console.error('Error fetching user trends:', err);
      setError('Failed to load user trends');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <ChartContainer title="User Growth Trends" icon={TrendingUp}>
        <div className="flex items-center justify-center h-64">
          <div className="loading loading-spinner loading-lg"></div>
        </div>
      </ChartContainer>
    );
  }

  if (error) {
    return (
      <ChartContainer title="User Growth Trends" icon={TrendingUp}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <button className="btn btn-sm btn-primary" onClick={fetchTrends}>
              Retry
            </button>
          </div>
        </div>
      </ChartContainer>
    );
  }

  const chartData = {
    labels: trends.trends.map(item => {
      const date = new Date(item._id);
      return timeframe === '1y' 
        ? date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
        : date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    }),
    datasets: [
      {
        label: 'New Users',
        data: trends.trends.map(item => item.newUsers),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: chartType === 'line'
      },
      {
        label: 'Students',
        data: trends.trends.map(item => item.students),
        borderColor: 'rgb(16, 185, 129)',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.4,
        fill: false
      },
      {
        label: 'Instructors',
        data: trends.trends.map(item => item.instructors),
        borderColor: 'rgb(245, 158, 11)',
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        tension: 0.4,
        fill: false
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: false,
      },
      tooltip: {
        mode: 'index',
        intersect: false,
      }
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Date'
        }
      },
      y: {
        display: true,
        title: {
          display: true,
          text: 'Number of Users'
        },
        beginAtZero: true
      }
    },
    interaction: {
      mode: 'nearest',
      axis: 'x',
      intersect: false
    }
  };

  const actions = (
    <div className="flex items-center gap-4">
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium text-gray-700">Chart:</span>
        <select
          value={chartType}
          onChange={(e) => setChartType(e.target.value)}
          className="select select-bordered select-sm w-auto"
        >
          <option value="line">Line</option>
          <option value="bar">Bar</option>
        </select>
      </div>
      <TimeframeSelector value={timeframe} onChange={setTimeframe} />
    </div>
  );

  const ChartComponent = chartType === 'line' ? Line : Bar;

  return (
    <ChartContainer
      title="User Growth Trends"
      subtitle={`User registration trends over ${timeframe}`}
      icon={TrendingUp}
      actions={actions}
    >
      <div className="h-80">
        <ChartComponent data={chartData} options={chartOptions} />
      </div>
      
      {/* Summary Stats */}
      <div className="mt-6 grid grid-cols-3 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-lg font-bold text-blue-600">
            {trends.trends.reduce((sum, item) => sum + item.newUsers, 0)}
          </div>
          <div className="text-sm text-gray-600">Total New Users</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold text-green-600">
            {trends.trends.reduce((sum, item) => sum + item.students, 0)}
          </div>
          <div className="text-sm text-gray-600">New Students</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold text-yellow-600">
            {trends.trends.reduce((sum, item) => sum + item.instructors, 0)}
          </div>
          <div className="text-sm text-gray-600">New Instructors</div>
        </div>
      </div>
    </ChartContainer>
  );
};

export default UserTrendsChart;
