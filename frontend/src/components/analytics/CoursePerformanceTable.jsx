import { useState, useEffect } from 'react';
import { analyticsAPI } from '../../utils/api';
import ChartContainer from './ChartContainer';
import { BookOpen, Star, Users, DollarSign, Eye, TrendingUp } from 'lucide-react';

const CoursePerformanceTable = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [sortBy, setSortBy] = useState('enrollments');
  const [limit, setLimit] = useState(10);

  useEffect(() => {
    fetchCoursePerformance();
  }, [sortBy, limit]);

  const fetchCoursePerformance = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await analyticsAPI.getCoursePerformance({ sortBy, limit });
      setCourses(response.data.courses);
    } catch (err) {
      console.error('Error fetching course performance:', err);
      setError('Failed to load course performance data');
    } finally {
      setLoading(false);
    }
  };

  const sortOptions = [
    { value: 'enrollments', label: 'Enrollments', icon: Users },
    { value: 'rating', label: 'Rating', icon: Star },
    { value: 'revenue', label: 'Revenue', icon: DollarSign },
    { value: 'completion', label: 'Completion Rate', icon: TrendingUp }
  ];

  const actions = (
    <div className="flex items-center gap-4">
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium text-gray-700">Sort by:</span>
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value)}
          className="select select-bordered select-sm w-auto"
        >
          {sortOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium text-gray-700">Show:</span>
        <select
          value={limit}
          onChange={(e) => setLimit(Number(e.target.value))}
          className="select select-bordered select-sm w-auto"
        >
          <option value={5}>Top 5</option>
          <option value={10}>Top 10</option>
          <option value={20}>Top 20</option>
        </select>
      </div>
    </div>
  );

  if (loading) {
    return (
      <ChartContainer title="Course Performance" icon={BookOpen} actions={actions}>
        <div className="flex items-center justify-center h-64">
          <div className="loading loading-spinner loading-lg"></div>
        </div>
      </ChartContainer>
    );
  }

  if (error) {
    return (
      <ChartContainer title="Course Performance" icon={BookOpen} actions={actions}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <button className="btn btn-sm btn-primary" onClick={fetchCoursePerformance}>
              Retry
            </button>
          </div>
        </div>
      </ChartContainer>
    );
  }

  const getCategoryColor = (category) => {
    const colors = {
      speaking: 'bg-blue-100 text-blue-800',
      writing: 'bg-green-100 text-green-800',
      reading: 'bg-purple-100 text-purple-800',
      listening: 'bg-yellow-100 text-yellow-800',
      'mock-tests': 'bg-red-100 text-red-800',
      comprehensive: 'bg-indigo-100 text-indigo-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  const getLevelColor = (level) => {
    const colors = {
      beginner: 'bg-green-100 text-green-800',
      intermediate: 'bg-yellow-100 text-yellow-800',
      advanced: 'bg-red-100 text-red-800',
      all: 'bg-blue-100 text-blue-800'
    };
    return colors[level] || 'bg-gray-100 text-gray-800';
  };

  return (
    <ChartContainer
      title="Course Performance"
      subtitle={`Top ${limit} courses sorted by ${sortOptions.find(o => o.value === sortBy)?.label.toLowerCase()}`}
      icon={BookOpen}
      actions={actions}
    >
      <div className="overflow-x-auto">
        <table className="table table-zebra w-full">
          <thead>
            <tr>
              <th>Course</th>
              <th>Category</th>
              <th>Level</th>
              <th>Instructor</th>
              <th className="text-center">
                <div className="flex items-center justify-center gap-1">
                  <Users className="w-4 h-4" />
                  Enrollments
                </div>
              </th>
              <th className="text-center">
                <div className="flex items-center justify-center gap-1">
                  <Star className="w-4 h-4" />
                  Rating
                </div>
              </th>
              <th className="text-center">
                <div className="flex items-center justify-center gap-1">
                  <TrendingUp className="w-4 h-4" />
                  Completion
                </div>
              </th>
              <th className="text-center">
                <div className="flex items-center justify-center gap-1">
                  <DollarSign className="w-4 h-4" />
                  Revenue
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            {courses.map((course, index) => (
              <tr key={course.id} className="hover">
                <td>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center text-blue-600 font-bold text-sm">
                      #{index + 1}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 line-clamp-2">
                        {course.title}
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        <Eye className="w-3 h-3 text-gray-400" />
                        <span className="text-xs text-gray-500">
                          {course.metrics.views} views
                        </span>
                      </div>
                    </div>
                  </div>
                </td>
                <td>
                  <span className={`badge badge-sm ${getCategoryColor(course.category)}`}>
                    {course.category}
                  </span>
                </td>
                <td>
                  <span className={`badge badge-sm ${getLevelColor(course.level)}`}>
                    {course.level}
                  </span>
                </td>
                <td>
                  <div className="text-sm">
                    {course.instructor?.firstName} {course.instructor?.lastName}
                  </div>
                </td>
                <td className="text-center">
                  <div className="font-semibold text-blue-600">
                    {course.metrics.enrollments.toLocaleString()}
                  </div>
                </td>
                <td className="text-center">
                  <div className="flex items-center justify-center gap-1">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <span className="font-semibold">
                      {course.metrics.averageRating.toFixed(1)}
                    </span>
                    <span className="text-xs text-gray-500">
                      ({course.metrics.totalRatings})
                    </span>
                  </div>
                </td>
                <td className="text-center">
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-12 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-500 h-2 rounded-full"
                        style={{ width: `${course.metrics.completionRate}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">
                      {course.metrics.completionRate}%
                    </span>
                  </div>
                </td>
                <td className="text-center">
                  <div className="font-semibold text-green-600">
                    ${course.metrics.revenue.toLocaleString()}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {courses.length === 0 && (
        <div className="text-center py-8">
          <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No course performance data available</p>
        </div>
      )}
    </ChartContainer>
  );
};

export default CoursePerformanceTable;
