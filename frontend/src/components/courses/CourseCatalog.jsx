import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { courseAPI } from '../../utils/api';
import {
  Search,
  Star,
  Clock,
  Users,
  Play,
  BookOpen,
  Heart,
  ShoppingCart
} from 'lucide-react';

const CourseCatalog = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedLevel, setSelectedLevel] = useState('all');
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const categories = [
    { id: 'all', name: 'All Courses' },
    { id: 'speaking', name: 'Speaking' },
    { id: 'writing', name: 'Writing' },
    { id: 'reading', name: 'Reading' },
    { id: 'listening', name: 'Listening' },
    { id: 'mock-tests', name: 'Mock Tests' },
  ];

  const levels = [
    { id: 'all', name: 'All Levels' },
    { id: 'beginner', name: 'Beginner' },
    { id: 'intermediate', name: 'Intermediate' },
    { id: 'advanced', name: 'Advanced' },
  ];

  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch real courses from API
      const response = await courseAPI.getCourses({
        status: 'published',
        limit: 20
      });

      if (response.success) {
        const coursesData = response.data.courses || response.data;

        // Map API data to component format
        const mappedCourses = coursesData.map(course => ({
          id: course._id || course.id,
          title: course.title,
          description: course.description || course.shortDescription,
          category: course.category,
          level: course.level,
          price: course.pricing?.price || 0,
          originalPrice: course.pricing?.originalPrice || course.pricing?.price || 0,
          rating: course.stats?.averageRating || 4.5,
          students: course.stats?.totalEnrollments || 0,
          duration: course.estimatedDuration ?
            `${course.estimatedDuration.hours}h ${course.estimatedDuration.minutes}m` :
            'Self-paced',
          lessons: course.lessons?.length || 'Multiple',
          instructor: course.instructor?.firstName && course.instructor?.lastName ?
            `${course.instructor.firstName} ${course.instructor.lastName}` :
            'Expert Instructor',
          thumbnail: course.thumbnail || '/api/placeholder/400/250',
          features: course.features || [],
          isVip: course.isVip || false,
          isPurchased: false, // TODO: Check user's enrolled courses
          isFavorite: false // TODO: Check user's favorites
        }));

        setCourses(mappedCourses);
      } else {
        throw new Error(response.message || 'Failed to fetch courses');
      }
    } catch (err) {
      console.error('Error fetching courses:', err);
      setError('Failed to load courses. Please try again.');

      // Fallback to a single sample course if API fails
      setCourses([
        {
          id: 'sample-1',
          title: 'PTE Academic Speaking Mastery',
          description: 'Master all speaking tasks with AI-powered feedback and scoring',
          category: 'speaking',
          level: 'intermediate',
          price: 99,
          originalPrice: 149,
          rating: 4.8,
          students: 1250,
          duration: '8 weeks',
          lessons: 24,
          instructor: 'Dr. Sarah Johnson',
          thumbnail: '/api/placeholder/400/250',
          features: ['AI Scoring', 'Live Sessions', 'Practice Tests'],
          isVip: true,
          isPurchased: false,
          isFavorite: false
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const filteredCourses = courses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || course.category === selectedCategory;
    const matchesLevel = selectedLevel === 'all' || course.level === selectedLevel || course.level === 'all';

    return matchesSearch && matchesCategory && matchesLevel;
  });

  const toggleFavorite = (courseId) => {
    setCourses(courses.map(course =>
      course.id === courseId
        ? { ...course, isFavorite: !course.isFavorite }
        : course
    ));
  };

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Course Catalog</h1>
            <p className="text-gray-600 mt-2">Loading courses...</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white rounded-xl shadow-sm border animate-pulse">
              <div className="aspect-video bg-gray-200 rounded-t-xl"></div>
              <div className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-8">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Course Catalog</h1>
            <p className="text-gray-600 mt-2">Discover courses to boost your PTE score</p>
          </div>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
          <BookOpen className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-red-900 mb-2">Error Loading Courses</h3>
          <p className="text-red-800 mb-4">{error}</p>
          <button
            onClick={fetchCourses}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Course Catalog</h1>
          <p className="text-gray-600 mt-2">Discover courses to boost your PTE score</p>
        </div>

        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search courses..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent w-64"
            />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-xl p-6 shadow-sm border">
        <div className="flex flex-wrap gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-[#00d4aa] focus:border-transparent w-full max-w-xs"
            >
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Level</label>
            <select
              value={selectedLevel}
              onChange={(e) => setSelectedLevel(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-[#00d4aa] focus:border-transparent w-full max-w-xs"
            >
              {levels.map(level => (
                <option key={level.id} value={level.id}>
                  {level.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Course Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCourses.map((course) => (
          <div key={course.id} className="course-card group">
            {/* Course Image */}
            <div className="relative aspect-video bg-gradient-to-br from-primary/20 to-accent/20 rounded-t-xl overflow-hidden">
              <div className="absolute inset-0 flex items-center justify-center">
                <Play className="w-16 h-16 text-primary" />
              </div>

              {/* VIP Badge */}
              {course.isVip && (
                <div className="absolute top-3 left-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                  VIP Course
                </div>
              )}

              {/* Favorite Button */}
              <button
                onClick={() => toggleFavorite(course.id)}
                className="absolute top-3 right-3 p-2 bg-white/90 rounded-full hover:bg-white transition-colors"
              >
                <Heart className={`w-4 h-4 ${course.isFavorite ? 'text-red-500 fill-current' : 'text-gray-600'}`} />
              </button>

              {/* Purchased Overlay */}
              {course.isPurchased && (
                <div className="absolute inset-0 bg-green-500/20 flex items-center justify-center">
                  <div className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Purchased
                  </div>
                </div>
              )}
            </div>

            {/* Course Content */}
            <div className="p-6">
              <div className="flex items-center gap-2 mb-2">
                <span className={`px-2 py-1 text-xs rounded-full ${
                  course.level === 'beginner' ? 'bg-green-100 text-green-800' :
                  course.level === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :
                  course.level === 'advanced' ? 'bg-red-100 text-red-800' :
                  'bg-blue-100 text-blue-800'
                }`}>
                  {course.level === 'all' ? 'All Levels' : course.level.charAt(0).toUpperCase() + course.level.slice(1)}
                </span>
                <span className="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">
                  {course.category.charAt(0).toUpperCase() + course.category.slice(1)}
                </span>
              </div>

              <h3 className="font-bold text-lg text-gray-900 mb-2 group-hover:text-primary transition-colors">
                {course.title}
              </h3>

              <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                {course.description}
              </p>

              {/* Course Stats */}
              <div className="flex items-center gap-4 text-sm text-gray-500 mb-4">
                <div className="flex items-center gap-1">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span>{course.rating}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Users className="w-4 h-4" />
                  <span>{course.students.toLocaleString()}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>{course.duration}</span>
                </div>
              </div>

              {/* Features */}
              <div className="flex flex-wrap gap-1 mb-4">
                {course.features.slice(0, 3).map((feature, index) => (
                  <span key={index} className="px-2 py-1 bg-primary/10 text-primary text-xs rounded">
                    {feature}
                  </span>
                ))}
              </div>

              {/* Price and Action */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-gray-900">${course.price}</span>
                  {course.originalPrice > course.price && (
                    <span className="text-sm text-gray-500 line-through">${course.originalPrice}</span>
                  )}
                </div>

                {course.isPurchased ? (
                  <Link
                    to={`/courses/${course.id}`}
                    className="bg-[#00d4aa] hover:bg-[#00d4aa]/90 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                  >
                    Continue
                  </Link>
                ) : (
                  <button className="bg-[#00d4aa] hover:bg-[#00d4aa]/90 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-1">
                    <ShoppingCart className="w-4 h-4" />
                    Enroll
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* No Results */}
      {filteredCourses.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No courses found</h3>
          <p className="text-gray-600">Try adjusting your search criteria</p>
        </div>
      )}
    </div>
  );
};

export default CourseCatalog;
