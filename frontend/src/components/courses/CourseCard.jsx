import { useState } from 'react';
import {
  Play,
  Users,
  Star,
  Clock,
  BookOpen,
  DollarSign,
  Eye,
  Edit,
  Trash2,
  MoreVertical,
  Crown,
  Calendar,
  TrendingUp
} from 'lucide-react';

const CourseCard = ({ 
  course, 
  onEdit, 
  onDelete, 
  onToggleStatus, 
  onView,
  isSelected,
  onSelect,
  showActions = true,
  variant = 'admin' // 'admin' or 'student'
}) => {
  const [showDropdown, setShowDropdown] = useState(false);

  const getStatusColor = (status) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'review':
        return 'bg-yellow-100 text-yellow-800';
      case 'archived':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category) => {
    const colors = {
      speaking: 'bg-blue-100 text-blue-800',
      writing: 'bg-green-100 text-green-800',
      reading: 'bg-purple-100 text-purple-800',
      listening: 'bg-orange-100 text-orange-800',
      'mock-tests': 'bg-red-100 text-red-800',
      comprehensive: 'bg-indigo-100 text-indigo-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  const formatPrice = (price, currency = 'USD') => {
    if (price === 0) return 'Free';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(price);
  };

  const formatDuration = (estimatedDuration) => {
    if (!estimatedDuration) return 'N/A';
    const { hours = 0, minutes = 0 } = estimatedDuration;
    if (hours > 0) {
      return `${hours}h ${minutes > 0 ? `${minutes}m` : ''}`;
    }
    return `${minutes}m`;
  };

  return (
    <div className={`
      bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden
      transition-all duration-200 hover:shadow-md hover:border-gray-300
      ${isSelected ? 'ring-2 ring-primary border-primary' : ''}
    `}>
      {/* Course Image */}
      <div className="relative aspect-video bg-gradient-to-br from-primary/20 to-accent/20">
        {course.thumbnail?.url ? (
          <img 
            src={course.thumbnail.url} 
            alt={course.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <Play className="w-16 h-16 text-primary/60" />
          </div>
        )}

        {/* Overlay badges */}
        <div className="absolute top-3 left-3 flex gap-2">
          {course.isVip && (
            <span className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-2 py-1 rounded-full text-xs font-semibold flex items-center gap-1">
              <Crown className="w-3 h-3" />
              VIP
            </span>
          )}
          <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getStatusColor(course.status)}`}>
            {course.status?.charAt(0).toUpperCase() + course.status?.slice(1)}
          </span>
        </div>

        {/* Actions dropdown */}
        {showActions && variant === 'admin' && (
          <div className="absolute top-3 right-3">
            <div className="relative">
              <button
                onClick={() => setShowDropdown(!showDropdown)}
                className="p-2 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-colors"
              >
                <MoreVertical className="w-4 h-4" />
              </button>

              {showDropdown && (
                <div className="absolute right-0 top-full mt-1 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10 min-w-[120px]">
                  <button
                    onClick={() => {
                      onView?.(course);
                      setShowDropdown(false);
                    }}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
                  >
                    <Eye className="w-4 h-4" />
                    View
                  </button>
                  <button
                    onClick={() => {
                      onEdit?.(course);
                      setShowDropdown(false);
                    }}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
                  >
                    <Edit className="w-4 h-4" />
                    Edit
                  </button>
                  <button
                    onClick={() => {
                      onToggleStatus?.(course._id, course.status === 'published' ? 'draft' : 'published');
                      setShowDropdown(false);
                    }}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50"
                  >
                    {course.status === 'published' ? 'Unpublish' : 'Publish'}
                  </button>
                  <hr className="my-1" />
                  <button
                    onClick={() => {
                      onDelete?.(course._id);
                      setShowDropdown(false);
                    }}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-red-50 text-red-600 flex items-center gap-2"
                  >
                    <Trash2 className="w-4 h-4" />
                    Delete
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Selection checkbox for admin */}
        {variant === 'admin' && onSelect && (
          <div className="absolute bottom-3 left-3">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={(e) => onSelect(course._id, e.target.checked)}
              className="w-4 h-4 text-primary bg-white border-gray-300 rounded focus:ring-primary focus:ring-2"
            />
          </div>
        )}
      </div>

      {/* Course Content */}
      <div className="p-4">
        {/* Category and Level */}
        <div className="flex items-center gap-2 mb-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(course.category)}`}>
            {course.category?.replace('-', ' ').toUpperCase()}
          </span>
          <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs font-medium">
            {course.level?.toUpperCase()}
          </span>
        </div>

        {/* Title */}
        <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
          {course.title}
        </h3>

        {/* Description */}
        <p className="text-sm text-gray-600 mb-3 line-clamp-2">
          {course.shortDescription || course.description}
        </p>

        {/* Instructor */}
        {course.instructor && (
          <div className="flex items-center gap-2 mb-3">
            <div className="w-6 h-6 bg-primary/20 rounded-full flex items-center justify-center">
              <span className="text-xs font-medium text-primary">
                {course.instructor.firstName?.[0]}{course.instructor.lastName?.[0]}
              </span>
            </div>
            <span className="text-sm text-gray-600">
              {course.instructor.firstName} {course.instructor.lastName}
            </span>
          </div>
        )}

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4 mb-3 text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <Users className="w-4 h-4" />
            <span>{course.stats?.totalEnrollments || 0} students</span>
          </div>
          <div className="flex items-center gap-1">
            <Clock className="w-4 h-4" />
            <span>{formatDuration(course.estimatedDuration)}</span>
          </div>
          <div className="flex items-center gap-1">
            <BookOpen className="w-4 h-4" />
            <span>{course.totalLessons || 0} lessons</span>
          </div>
          <div className="flex items-center gap-1">
            <Star className="w-4 h-4 text-yellow-500" />
            <span>{course.stats?.averageRating?.toFixed(1) || '0.0'}</span>
          </div>
        </div>

        {/* Price and Revenue (Admin view) */}
        {variant === 'admin' && (
          <div className="flex items-center justify-between pt-3 border-t border-gray-100">
            <div className="flex items-center gap-1 text-sm">
              <DollarSign className="w-4 h-4 text-green-600" />
              <span className="font-semibold text-green-600">
                {formatPrice(course.pricing?.price, course.pricing?.currency)}
              </span>
            </div>
            <div className="flex items-center gap-1 text-sm text-gray-600">
              <TrendingUp className="w-4 h-4" />
              <span>${course.stats?.totalRevenue?.toLocaleString() || '0'}</span>
            </div>
          </div>
        )}

        {/* Price (Student view) */}
        {variant === 'student' && (
          <div className="flex items-center justify-between pt-3 border-t border-gray-100">
            <div className="flex items-center gap-2">
              {course.pricing?.originalPrice && course.pricing.originalPrice > course.pricing.price && (
                <span className="text-sm text-gray-500 line-through">
                  {formatPrice(course.pricing.originalPrice, course.pricing.currency)}
                </span>
              )}
              <span className="font-semibold text-primary">
                {formatPrice(course.pricing?.price, course.pricing?.currency)}
              </span>
            </div>
            {course.pricing?.discountPercentage && (
              <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-semibold">
                {course.pricing.discountPercentage}% OFF
              </span>
            )}
          </div>
        )}

        {/* Last updated (Admin view) */}
        {variant === 'admin' && (
          <div className="flex items-center gap-1 text-xs text-gray-500 mt-2">
            <Calendar className="w-3 h-3" />
            <span>Updated {new Date(course.updatedAt).toLocaleDateString()}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default CourseCard;
