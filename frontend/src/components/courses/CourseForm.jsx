import { useState, useEffect } from 'react';
import {
  X,
  Upload,
  Play,
  Save,
  Eye,
  AlertCircle,
  Plus,
  Trash2
} from 'lucide-react';

const CourseForm = ({ 
  course = null, 
  isOpen, 
  onClose, 
  onSave,
  mode = 'create' // 'create' or 'edit'
}) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    shortDescription: '',
    category: 'speaking',
    level: 'beginner',
    tags: [],
    pricing: {
      type: 'paid',
      price: 0,
      originalPrice: 0,
      currency: 'USD',
      discountPercentage: 0
    },
    estimatedDuration: {
      hours: 0,
      minutes: 0
    },
    features: [],
    learningOutcomes: [],
    prerequisites: [],
    isVip: false,
    allowPreview: true,
    maxEnrollments: null
  });

  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [newTag, setNewTag] = useState('');
  const [newFeature, setNewFeature] = useState('');
  const [newOutcome, setNewOutcome] = useState('');
  const [newPrerequisite, setNewPrerequisite] = useState('');

  // Initialize form data when course prop changes
  useEffect(() => {
    if (course && mode === 'edit') {
      setFormData({
        title: course.title || '',
        description: course.description || '',
        shortDescription: course.shortDescription || '',
        category: course.category || 'speaking',
        level: course.level || 'beginner',
        tags: course.tags || [],
        pricing: {
          type: course.pricing?.type || 'paid',
          price: course.pricing?.price || 0,
          originalPrice: course.pricing?.originalPrice || 0,
          currency: course.pricing?.currency || 'USD',
          discountPercentage: course.pricing?.discountPercentage || 0
        },
        estimatedDuration: {
          hours: course.estimatedDuration?.hours || 0,
          minutes: course.estimatedDuration?.minutes || 0
        },
        features: course.features || [],
        learningOutcomes: course.learningOutcomes || [],
        prerequisites: course.prerequisites || [],
        isVip: course.isVip || false,
        allowPreview: course.allowPreview !== false,
        maxEnrollments: course.maxEnrollments || null
      });
    } else if (mode === 'create') {
      // Reset form for create mode
      setFormData({
        title: '',
        description: '',
        shortDescription: '',
        category: 'speaking',
        level: 'beginner',
        tags: [],
        pricing: {
          type: 'paid',
          price: 0,
          originalPrice: 0,
          currency: 'USD',
          discountPercentage: 0
        },
        estimatedDuration: {
          hours: 0,
          minutes: 0
        },
        features: [],
        learningOutcomes: [],
        prerequisites: [],
        isVip: false,
        allowPreview: true,
        maxEnrollments: null
      });
    }
    setErrors({});
  }, [course, mode]);

  // Handle input changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  // Handle nested object changes (pricing, estimatedDuration)
  const handleNestedChange = (parent, field, value) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [field]: value
      }
    }));
  };

  // Handle array operations
  const addToArray = (field, value, setter) => {
    if (value.trim()) {
      setFormData(prev => ({
        ...prev,
        [field]: [...prev[field], value.trim()]
      }));
      setter('');
    }
  };

  const removeFromArray = (field, index) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }));
  };

  // Validation
  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Course title is required';
    } else if (formData.title.length > 100) {
      newErrors.title = 'Title cannot exceed 100 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Course description is required';
    } else if (formData.description.length > 1000) {
      newErrors.description = 'Description cannot exceed 1000 characters';
    }

    if (formData.shortDescription && formData.shortDescription.length > 200) {
      newErrors.shortDescription = 'Short description cannot exceed 200 characters';
    }

    if (formData.pricing.type === 'paid' && formData.pricing.price <= 0) {
      newErrors.price = 'Price must be greater than 0 for paid courses';
    }

    if (formData.pricing.originalPrice && formData.pricing.originalPrice < formData.pricing.price) {
      newErrors.originalPrice = 'Original price must be greater than or equal to current price';
    }

    if (formData.maxEnrollments && formData.maxEnrollments <= 0) {
      newErrors.maxEnrollments = 'Max enrollments must be greater than 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('Failed to save course:', error);
      setErrors({ submit: error.message || 'Failed to save course' });
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {mode === 'create' ? 'Create New Course' : 'Edit Course'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Course Title *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  className={`form-input ${errors.title ? 'border-red-500' : ''}`}
                  placeholder="Enter course title"
                />
                {errors.title && (
                  <p className="text-red-500 text-sm mt-1 flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {errors.title}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Short Description
                </label>
                <input
                  type="text"
                  value={formData.shortDescription}
                  onChange={(e) => handleInputChange('shortDescription', e.target.value)}
                  className={`form-input ${errors.shortDescription ? 'border-red-500' : ''}`}
                  placeholder="Brief description for course cards"
                />
                {errors.shortDescription && (
                  <p className="text-red-500 text-sm mt-1">{errors.shortDescription}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Full Description *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={4}
                  className={`form-textarea ${errors.description ? 'border-red-500' : ''}`}
                  placeholder="Detailed course description"
                />
                {errors.description && (
                  <p className="text-red-500 text-sm mt-1">{errors.description}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Category *
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    className="form-select"
                  >
                    <option value="speaking">Speaking</option>
                    <option value="writing">Writing</option>
                    <option value="reading">Reading</option>
                    <option value="listening">Listening</option>
                    <option value="mock-tests">Mock Tests</option>
                    <option value="comprehensive">Comprehensive</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Level *
                  </label>
                  <select
                    value={formData.level}
                    onChange={(e) => handleInputChange('level', e.target.value)}
                    className="form-select"
                  >
                    <option value="beginner">Beginner</option>
                    <option value="intermediate">Intermediate</option>
                    <option value="advanced">Advanced</option>
                    <option value="all">All Levels</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Pricing */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Pricing</h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Pricing Type
                </label>
                <select
                  value={formData.pricing.type}
                  onChange={(e) => handleNestedChange('pricing', 'type', e.target.value)}
                  className="form-select"
                >
                  <option value="free">Free</option>
                  <option value="paid">Paid</option>
                  <option value="subscription">Subscription</option>
                </select>
              </div>

              {formData.pricing.type !== 'free' && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Price *
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.pricing.price}
                      onChange={(e) => handleNestedChange('pricing', 'price', parseFloat(e.target.value) || 0)}
                      className={`form-input ${errors.price ? 'border-red-500' : ''}`}
                    />
                    {errors.price && (
                      <p className="text-red-500 text-sm mt-1">{errors.price}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Original Price
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.pricing.originalPrice}
                      onChange={(e) => handleNestedChange('pricing', 'originalPrice', parseFloat(e.target.value) || 0)}
                      className={`form-input ${errors.originalPrice ? 'border-red-500' : ''}`}
                    />
                    {errors.originalPrice && (
                      <p className="text-red-500 text-sm mt-1">{errors.originalPrice}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Currency
                    </label>
                    <select
                      value={formData.pricing.currency}
                      onChange={(e) => handleNestedChange('pricing', 'currency', e.target.value)}
                      className="form-select"
                    >
                      <option value="USD">USD</option>
                      <option value="EUR">EUR</option>
                      <option value="GBP">GBP</option>
                      <option value="AUD">AUD</option>
                      <option value="CAD">CAD</option>
                    </select>
                  </div>
                </div>
              )}
            </div>

            {/* Duration */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Course Duration</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Hours
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={formData.estimatedDuration.hours}
                    onChange={(e) => handleNestedChange('estimatedDuration', 'hours', parseInt(e.target.value) || 0)}
                    className="form-input"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Minutes
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="59"
                    value={formData.estimatedDuration.minutes}
                    onChange={(e) => handleNestedChange('estimatedDuration', 'minutes', parseInt(e.target.value) || 0)}
                    className="form-input"
                  />
                </div>
              </div>
            </div>

            {/* Tags */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Tags</h3>
              
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="Add a tag"
                  className="form-input flex-1"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addToArray('tags', newTag, setNewTag);
                    }
                  }}
                />
                <button
                  type="button"
                  onClick={() => addToArray('tags', newTag, setNewTag)}
                  className="btn-secondary"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>

              {formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm flex items-center gap-2"
                    >
                      {tag}
                      <button
                        type="button"
                        onClick={() => removeFromArray('tags', index)}
                        className="hover:text-red-600"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>

            {/* Course Settings */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Course Settings</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    id="isVip"
                    checked={formData.isVip}
                    onChange={(e) => handleInputChange('isVip', e.target.checked)}
                    className="form-checkbox"
                  />
                  <label htmlFor="isVip" className="text-sm font-medium text-gray-700">
                    VIP Course
                  </label>
                </div>

                <div className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    id="allowPreview"
                    checked={formData.allowPreview}
                    onChange={(e) => handleInputChange('allowPreview', e.target.checked)}
                    className="form-checkbox"
                  />
                  <label htmlFor="allowPreview" className="text-sm font-medium text-gray-700">
                    Allow Preview
                  </label>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Enrollments (optional)
                </label>
                <input
                  type="number"
                  min="1"
                  value={formData.maxEnrollments || ''}
                  onChange={(e) => handleInputChange('maxEnrollments', e.target.value ? parseInt(e.target.value) : null)}
                  className={`form-input ${errors.maxEnrollments ? 'border-red-500' : ''}`}
                  placeholder="Leave empty for unlimited"
                />
                {errors.maxEnrollments && (
                  <p className="text-red-500 text-sm mt-1">{errors.maxEnrollments}</p>
                )}
              </div>
            </div>

            {/* Submit Error */}
            {errors.submit && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-red-800 flex items-center gap-2">
                  <AlertCircle className="w-4 h-4" />
                  {errors.submit}
                </p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200 bg-gray-50">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn-primary flex items-center gap-2"
              disabled={loading}
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save className="w-4 h-4" />
              )}
              {mode === 'create' ? 'Create Course' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CourseForm;
