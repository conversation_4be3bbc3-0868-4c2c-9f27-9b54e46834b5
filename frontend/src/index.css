@import "tailwindcss";

@layer components {
  .btn-primary {
    @apply bg-[#00d4aa] hover:bg-[#00d4aa]/90 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors duration-200;
  }

  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200;
  }

  .card-shadow {
    @apply shadow-lg hover:shadow-xl transition-shadow duration-300;
  }

  .sidebar-item {
    @apply flex items-center gap-3 px-4 py-3 rounded-lg hover:bg-gray-100 transition-colors duration-200;
  }

  .sidebar-item.active {
    @apply bg-[#00d4aa] text-white;
  }

  .course-card {
    @apply bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden;
  }

  .practice-module {
    @apply bg-gradient-to-br from-[#00d4aa]/10 to-[#4ecdc4]/10 rounded-lg p-6 hover:shadow-md transition-all duration-300;
  }

  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#00d4aa] focus:border-transparent transition-colors duration-200;
  }

  .form-textarea {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#00d4aa] focus:border-transparent transition-colors duration-200 resize-y;
  }

  .form-select {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#00d4aa] focus:border-transparent transition-colors duration-200 bg-white;
  }

  .form-checkbox {
    @apply w-4 h-4 text-[#00d4aa] bg-white border-gray-300 rounded focus:ring-[#00d4aa] focus:ring-2;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
