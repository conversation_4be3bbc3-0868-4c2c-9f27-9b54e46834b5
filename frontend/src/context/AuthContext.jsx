import { createContext, useContext, useState, useEffect } from 'react';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api/v1';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is logged in on app start
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      // Check localStorage for saved user data
      const savedUser = localStorage.getItem('pte_user');
      const savedToken = localStorage.getItem('pte_token');

      if (savedUser && savedToken) {
        const userData = JSON.parse(savedUser);
        setUser(userData);
        setIsAuthenticated(true);

        // TODO: Validate token with backend
        // const response = await api.validateToken(savedToken);
        // if (!response.valid) {
        //   logout();
        // }
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      logout();
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email, password, googleData = null) => {
    try {
      setIsLoading(true);

      let response;

      if (googleData) {
        // Google login - data already received from backend
        response = { success: true, data: googleData };
      } else {
        // Regular email/password login
        const result = await fetch(`${API_BASE_URL}/auth/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ email, password }),
        });

        // Check if the request was successful
        if (!result.ok) {
          const errorData = await result.json();
          throw new Error(errorData.message || `HTTP error! status: ${result.status}`);
        }

        response = await result.json();
      }

      if (response.success) {
        const { user, accessToken } = response.data;

        // Save to localStorage
        localStorage.setItem('pte_user', JSON.stringify(user));
        localStorage.setItem('pte_token', accessToken);

        setUser(user);
        setIsAuthenticated(true);

        return { success: true, user };
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login failed:', error);

      // Check if it's a network/CORS error
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        return { success: false, error: 'Network error. Please check if the server is running and CORS is configured correctly.' };
      }

      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData) => {
    try {
      setIsLoading(true);

      // Make actual API call to register endpoint
      const response = await fetch(`${API_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(userData),
      });

      // Check if the request was successful
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        const { user, accessToken } = result.data;

        // Save to localStorage
        localStorage.setItem('pte_user', JSON.stringify(user));
        localStorage.setItem('pte_token', accessToken);

        setUser(user);
        setIsAuthenticated(true);

        return { success: true, user };
      } else {
        throw new Error(result.message || 'Registration failed');
      }
    } catch (error) {
      console.error('Registration failed:', error);

      // Check if it's a network/CORS error
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        return { success: false, error: 'Network error. Please check if the server is running and CORS is configured correctly.' };
      }

      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      // Get the token for the logout request
      const token = localStorage.getItem('pte_token');

      // Call backend logout endpoint to clear cookies
      await fetch(`${API_BASE_URL}/auth/logout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` })
        },
        credentials: 'include',
      });
    } catch (error) {
      console.error('Logout API call failed:', error);
      // Continue with local logout even if API call fails
    } finally {
      // Clear local storage and state
      localStorage.removeItem('pte_user');
      localStorage.removeItem('pte_token');
      setUser(null);
      setIsAuthenticated(false);
    }
  };

  const updateUser = (updatedData) => {
    const updatedUser = { ...user, ...updatedData };
    setUser(updatedUser);
    localStorage.setItem('pte_user', JSON.stringify(updatedUser));
  };

  const loginWithSSO = async (provider) => {
    try {
      setIsLoading(true);

      // Redirect to backend SSO endpoint
      const ssoUrl = `${API_BASE_URL}/auth/${provider}`;

      // For now, show a message that SSO is not fully implemented
      console.log(`SSO login with ${provider} - redirecting to: ${ssoUrl}`);

      // In a real implementation, you would:
      // 1. Redirect to the SSO provider
      // 2. Handle the callback
      // 3. Exchange the code for tokens

      // For development, return an error message
      return {
        success: false,
        error: `${provider} SSO is not fully implemented yet. Please use email/password login.`
      };

    } catch (error) {
      console.error('SSO login failed:', error);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  };

  const value = {
    user,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    updateUser,
    loginWithSSO,
    checkAuthStatus
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
