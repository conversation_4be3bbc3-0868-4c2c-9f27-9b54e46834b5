{"name": "pte-proficiency", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@google-cloud/local-auth": "^3.0.1", "@hookform/resolvers": "^5.0.1", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.7", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "chart.js": "^4.4.9", "daisyui": "^5.0.37", "google-auth-library": "^9.15.1", "lucide-react": "^0.511.0", "postcss": "^8.5.3", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-router-dom": "^7.6.1", "tailwindcss": "^4.1.7", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.27.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.0.0", "prettier": "^3.5.3", "vite": "^6.3.5"}}