# PTE Proficiency Learning Platform

A comprehensive web application for PTE (Pearson Test of English) preparation, designed for university admissions and migration purposes.

## Features

### 🔐 Authentication System
- **Multi-provider SSO**: Google, Microsoft, and Apple authentication
- **Standalone accounts**: Traditional email/password registration
- **Role-based access**: Student and Administrator accounts
- **Protected routes**: Secure access to dashboard and courses

### 📚 Learning Management
- **Course Catalog**: Browse and filter PTE preparation courses
- **Progress Tracking**: Monitor learning progress and achievements
- **Practice Modules**: Interactive exercises for all PTE sections:
  - Speaking (Read Aloud, Repeat Sentence, Describe Image, etc.)
  - Writing (Summarize Written Text, Write Essay)
  - Reading (Multiple Choice, Fill in the Blanks)
  - Listening (Summarize Spoken Text, etc.)

### 💳 Payment & Enrollment
- **Course Enrollment**: Secure payment processing for course access
- **VIP Courses**: Premium content with advanced features
- **Purchase History**: Track enrolled courses and payments

### 👨‍💼 Admin Dashboard
- **Course Management**: Create, edit, and manage course content
- **User Management**: Monitor student progress and engagement
- **Analytics**: Track course sales and performance statistics
- **Content Control**: Enable/disable courses and features

## Tech Stack

### Frontend
- **React 18** - Modern UI library with hooks
- **Vite** - Fast build tool and development server
- **React Router** - Client-side routing
- **Tailwind CSS 4** - Utility-first CSS framework
- **daisyUI 5** - Component library for Tailwind CSS
- **Lucide React** - Beautiful icon library

### Form Management & Validation
- **React Hook Form** - Performant forms with easy validation
- **Yup** - Schema validation library

### Development Tools
- **ESLint** - Code linting and quality checks
- **Prettier** - Code formatting
- **Hot Module Replacement** - Fast development experience

## Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd pte-proficiency
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   Navigate to `http://localhost:5173`

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues

## Project Structure

```
src/
├── components/
│   ├── auth/           # Authentication components
│   ├── courses/        # Course-related components
│   ├── dashboard/      # Dashboard components
│   └── layout/         # Layout components
├── context/            # React context providers
├── hooks/              # Custom React hooks
├── utils/              # Utility functions
├── tailwind.css        # Tailwind CSS configuration
└── main.jsx           # Application entry point
```

## Authentication Flow

1. **Login/Register**: Users can create accounts or sign in
2. **SSO Integration**: Support for Google, Microsoft, and Apple
3. **Role Assignment**: Automatic role detection (student/admin)
4. **Protected Access**: Route protection based on authentication status
5. **Session Management**: Persistent login with localStorage

## Course Management

### For Students
- Browse course catalog with filtering options
- Enroll in courses with secure payment
- Track progress and achievements
- Access practice modules and mock tests

### For Administrators
- Create and manage course content
- Monitor student enrollment and progress
- Generate analytics and reports
- Control course availability and pricing

## Design System

The application uses a consistent design system based on:
- **Primary Color**: `#00D4AA` (Teal green)
- **Secondary Color**: `#FF6B6B` (Coral red)
- **Accent Color**: `#4ECDC4` (Light teal)
- **Typography**: Inter font family
- **Components**: daisyUI component library

## Future Enhancements

- [ ] Real-time AI scoring for speaking and writing
- [ ] Video conferencing for live classes
- [ ] Mobile app development
- [ ] Advanced analytics dashboard
- [ ] Multilingual support
- [ ] Offline practice mode
- [ ] Social learning features
- [ ] Gamification elements

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please contact:
- Email: <EMAIL>
- Documentation: [Link to docs]
- Issues: [GitHub Issues]
